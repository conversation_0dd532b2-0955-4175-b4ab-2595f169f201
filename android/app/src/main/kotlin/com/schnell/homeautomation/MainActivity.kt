package com.schnell.homeautomation

import android.app.Activity
import android.content.BroadcastReceiver
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.IntentSender
import android.os.Bundle
import android.util.Log
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.google.android.gms.home.matter.Matter
import com.google.android.gms.home.matter.commissioning.CommissioningRequest
import io.flutter.embedding.android.FlutterActivity
import io.flutter.plugin.common.MethodChannel

class MainActivity : FlutterActivity() {
    private val CHANNEL = "com.schnell.homeautomation/commission"
    private lateinit var resultCallback: MethodChannel.Result
    private lateinit var methodChannel: MethodChannel
    private var commissioningService: MatterCommissioningService? = null
    private val passcodeReceiver =
            object : BroadcastReceiver() {
                override fun onReceive(context: Context?, intent: Intent?) {
                    val passcode = intent?.getStringExtra("passcode")
                    Log.d("MainActivityPage", "Received passcode: $passcode")
                    methodChannel.invokeMethod("onPasscodeReceived", passcode)
                }
            }
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val filter = IntentFilter("com.schnell.homeautomationPASSCODE_RECEIVER")
        LocalBroadcastManager.getInstance(this).registerReceiver(passcodeReceiver, filter)
        methodChannel = MethodChannel(flutterEngine!!.dartExecutor.binaryMessenger, CHANNEL)

        methodChannel.setMethodCallHandler { call, result ->
            when (call.method) {
                "startCommissioning" -> {
                    resultCallback = result
                    startCommissioning()
                }
                else -> result.notImplemented()
            }
        }
    }
    override fun onDestroy() {
        super.onDestroy()
        LocalBroadcastManager.getInstance(this).unregisterReceiver(passcodeReceiver)
    }

    private fun startCommissioning() {
        val request =
                CommissioningRequest.builder()
                        .setCommissioningService(
                                ComponentName(this, MatterCommissioningService::class.java)
                        )
                        .build()

        Matter.getCommissioningClient(this)
                .commissionDevice(request)
                .addOnSuccessListener { intentSender ->
                    try {
                        startIntentSenderForResult(intentSender, 10001, null, 0, 0, 0)
                        Log.d("MainActivityPage", "Commissioning UI launched")
                        resultCallback.success("Commissioning UI launched")
                    } catch (e: IntentSender.SendIntentException) {
                        Log.e("MainActivityPage", "Failed to launch commissioning UI", e)
                        resultCallback.error(
                                "LAUNCH_FAILED",
                                "Could not launch commissioning UI",
                                e.toString()
                        )
                    }
                }
                .addOnFailureListener { error ->
                    Log.e("MainActivityPage", "Commissioning request failed", error)
                    resultCallback.error(
                            "COMMISSION_FAILED",
                            "Commissioning request failed",
                            error.toString()
                    )
                }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == 10001 && resultCode == Activity.RESULT_OK && data != null) {
            val extras = data.extras
            val resultData = mutableMapOf<String, Any?>()

            extras?.keySet()?.forEach { key ->
                resultData[key] = extras.get(key)
                Log.d("CommissioningResult", "$key -> ${extras.get(key)}")
            }

            methodChannel.invokeMethod("onCommissioningSuccess", resultData)
        } else {
            Log.e("CommissioningResult", "Commissioning canceled or failed")
        }
    }
}
