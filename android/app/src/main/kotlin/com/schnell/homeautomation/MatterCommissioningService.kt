package com.schnell.homeautomation

import android.app.Service
import android.content.Intent
import android.os.IBinder
import android.util.Log
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.google.android.gms.home.matter.commissioning.CommissioningCompleteMetadata
import com.google.android.gms.home.matter.commissioning.CommissioningRequestMetadata
import com.google.android.gms.home.matter.commissioning.CommissioningService
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch

class MatterCommissioningService : Service(), CommissioningService.Callback {

    private val serviceScope = CoroutineScope(Dispatchers.Main + Job())
    private lateinit var commissioningServiceDelegate: CommissioningService

    override fun onCreate() {
        super.onCreate()
        Log.d("MatterCommissioning", "Service created")
        commissioningServiceDelegate = CommissioningService.Builder(this).setCallback(this).build()
    }

    override fun onBind(intent: Intent?): IBinder {
        return commissioningServiceDelegate.asBinder()
    }

    override fun onDestroy() {
        super.onDestroy()
        serviceScope.cancel()
    }

    override fun onCommissioningRequested(metadata: CommissioningRequestMetadata) {
        Log.d("MatterCommissioning", "onCommissioningRequested: ${metadata.passcode}")
        val intent = Intent("com.schnell.homeautomationPASSCODE_RECEIVER")
        intent.putExtra("passcode", metadata.passcode.toString())
        LocalBroadcastManager.getInstance(this).sendBroadcast(intent)
        Log.d("MatterCommissioning", "Broadcasting passcode: ${metadata.passcode}")
        serviceScope.launch {
            commissioningServiceDelegate.sendCommissioningComplete(
                    CommissioningCompleteMetadata.Builder().build()
            )
        }
    }
}
