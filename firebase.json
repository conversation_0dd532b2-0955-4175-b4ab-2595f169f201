{"flutter": {"platforms": {"android": {"default": {"projectId": "schnell-home-automation", "appId": "1:685811404746:android:25130d7aa7640771404933", "fileOutput": "android/app/google-services.json"}}, "ios": {"default": {"projectId": "schnell-home-automation", "appId": "1:685811404746:ios:63a57d118c252d3a404933", "uploadDebugSymbols": true, "fileOutput": "ios/Runner/GoogleService-Info.plist"}}, "dart": {"lib/firebase_options.dart": {"projectId": "schnell-home-automation", "configurations": {"android": "1:685811404746:android:25130d7aa7640771404933", "ios": "1:685811404746:ios:63a57d118c252d3a404933", "macos": "1:685811404746:ios:63a57d118c252d3a404933", "web": "1:685811404746:web:88bf523f43923de4404933", "windows": "1:685811404746:web:88bf523f43923de4404933"}}}, "macos": {"default": {"projectId": "schnell-home-automation", "appId": "1:685811404746:ios:63a57d118c252d3a404933", "uploadDebugSymbols": true, "fileOutput": "macos/Runner/GoogleService-Info.plist"}}}}}