# Matter Device Binding Guide

## Overview

This Flutter app now includes comprehensive Matter device binding functionality that allows you to create direct bindings between Matter devices (like switches and lights) without requiring Home Assistant or API calls for device control.

## What is Matter Binding?

Matter binding creates a direct relationship between two Matter devices on the same fabric, allowing one device (controller) to directly control another device (controlled) without going through a hub or cloud service. For example, you can bind a Matter switch to a Matter light so that pressing the switch directly controls the light, even if Home Assistant is offline.

## Features Implemented

### 1. **Matter Device Management**
- View all commissioned Matter devices
- Commission devices to CHIPTool using share codes
- Track device commissioning status
- Support for multiple device types (lights, switches, fans)

### 2. **Device Commissioning to CHIPTool**
- Commission devices using Home Assistant share codes
- Assign unique node IDs to devices
- Automatic status tracking
- Integration with CHIPTool addon

### 3. **Binding Creation**
- Create bindings between controller and controlled devices
- Automatic access control list (ACL) setup
- Support for different Matter clusters (On/Off, Fan Control, etc.)
- Real-time binding status tracking

### 4. **Binding Management**
- View all active bindings
- Remove bindings when no longer needed
- Track binding creation dates
- Visual status indicators

## How to Use

### Step 1: Access Matter Binding
1. Open the app and navigate to the Virtual Dock screen
2. Tap the **Link icon** (🔗) in the app bar
3. This opens the Matter Binding screen with three tabs

### Step 2: Commission Devices to CHIPTool

Before creating bindings, you need to commission your Matter devices to CHIPTool:

1. **Get Share Codes from Home Assistant:**
   - Go to Home Assistant
   - Navigate to your Matter device
   - Click "Share Device" to get the share code

2. **Commission to CHIPTool:**
   - In the app, go to the "Devices" tab
   - Find your device and tap "Commission to CHIPTool"
   - Enter the share code from Home Assistant
   - Assign a unique node ID (e.g., 100, 101, 102...)
   - Tap "Commission"

3. **Repeat for all devices** you want to bind

### Step 3: Create Bindings

1. Go to the "Create Binding" tab
2. Select a **Controller Device** (typically a switch)
3. Select a **Controlled Device** (light, fan, etc.)
4. Tap "Create Binding"

The app will automatically:
- Set up access control on the controlled device
- Create the binding relationship
- Configure the appropriate Matter cluster

### Step 4: Manage Bindings

1. Go to the "Bindings" tab to view all active bindings
2. See binding status and creation dates
3. Remove bindings by tapping the delete icon

## Technical Implementation

### Architecture

The Matter binding feature consists of several key components:

1. **Models** (`lib/models/matter_device.dart`)
   - `MatterDevice`: Represents a Matter device with commissioning info
   - `MatterBinding`: Represents a binding relationship
   - `MatterEndpoint`: Device endpoint information

2. **Service** (`lib/services/matter_binding_service.dart`)
   - Handles CHIPTool communication
   - Manages device commissioning
   - Creates and removes bindings
   - Executes Matter commands

3. **Provider** (`lib/providers/matter_binding_provider.dart`)
   - State management for devices and bindings
   - Reactive UI updates
   - Error handling

4. **UI** (`lib/screens/matter_binding_screen.dart`)
   - Three-tab interface
   - Device commissioning dialogs
   - Binding creation forms
   - Binding management

### CHIPTool Integration

The app communicates with the CHIPTool addon through HTTP API calls:

```dart
// Commission device
chip-tool pairing code <node_id> <share_code>

// Set up access control
chip-tool accesscontrol write acl '[...]' <controlled_node_id> 0

// Create binding
chip-tool binding write binding '[...]' <controller_node_id> <endpoint>
```

### Matter Clusters Supported

- **On/Off Cluster (6)**: Basic on/off control
- **Level Control Cluster (8)**: Dimming control
- **Fan Control Cluster (514)**: Fan speed control
- **Color Control Cluster (768)**: Color control (future)

## Prerequisites

### 1. CHIPTool Addon Installation

Install the CHIPTool addon in Home Assistant:

1. Go to Home Assistant → Add-ons
2. Add repository: `https://github.com/home-assistant/addons-development`
3. Install "CHIPTool" addon
4. Start the addon
5. Access via: `http://homeassistant.local:8123/77f1785d_chip_tool/ingress`

### 2. Matter Device Requirements

- Devices must be Matter-compatible
- Devices must be commissioned to Home Assistant first
- Devices must support the required Matter clusters
- Controller devices need binding cluster support

### 3. Network Requirements

- All devices on the same Matter fabric
- Home Assistant and CHIPTool accessible
- Stable network connection during commissioning

## Troubleshooting

### Common Issues

1. **"Device not commissioned to CHIPTool"**
   - Ensure you have the correct share code
   - Check that the node ID is unique
   - Verify CHIPTool addon is running

2. **"Failed to create binding"**
   - Ensure both devices are commissioned to CHIPTool
   - Check that devices support the required clusters
   - Verify network connectivity

3. **"Binding not working"**
   - Check that devices are on the same Matter fabric
   - Verify the binding was created successfully
   - Test device functionality independently

### Debug Steps

1. Check CHIPTool addon logs
2. Verify device commissioning status
3. Test individual device commands
4. Check Matter fabric consistency

## Example Workflow

Here's a complete example of binding a Matter switch to a Matter light:

1. **Commission Switch:**
   - Get share code from HA for switch
   - Commission to CHIPTool with node ID 100

2. **Commission Light:**
   - Get share code from HA for light
   - Commission to CHIPTool with node ID 101

3. **Create Binding:**
   - Controller: Switch (Node 100)
   - Controlled: Light (Node 101)
   - Cluster: On/Off (6)

4. **Test:**
   - Press physical switch
   - Light should respond directly
   - Works even if HA is offline

## Benefits

- **Direct Control**: No hub dependency for basic operations
- **Faster Response**: Eliminates network latency
- **Reliability**: Works even if Home Assistant is offline
- **Energy Efficient**: Reduces network traffic
- **Standard Compliant**: Uses official Matter binding protocol

## Future Enhancements

- Support for more Matter clusters
- Group bindings (one controller, multiple controlled devices)
- Scene bindings
- Advanced endpoint configuration
- Binding templates for common scenarios
- Bulk commissioning tools

## Support

For issues or questions:
1. Check the troubleshooting section
2. Review CHIPTool addon documentation
3. Verify Matter device compatibility
4. Check Home Assistant Matter integration status
