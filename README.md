# Schnell Home Automation

A Flutter-based smart home control application that connects to Home Assistant for controlling and monitoring various home devices.

![Flutter](https://img.shields.io/badge/Flutter-3.x-blue)
![Dart](https://img.shields.io/badge/Dart-3.x-blue)
![Home Assistant](https://img.shields.io/badge/Home_Assistant-Integration-green)
![Firebase](https://img.shields.io/badge/Firebase-Integrated-orange)

## Overview

Schnell Home Automation is a mobile application designed to provide a user-friendly interface for controlling smart home devices through Home Assistant. The app features a virtual dock interface that allows users to control various devices such as lights, fans, power switches, and execute predefined scenes.

## Features

- **Device Control**: Control various smart home devices:
  - **Lights**: Toggle on/off and adjust brightness
  - **Fans**: Toggle on/off and adjust speed levels
  - **Power Switches**: Toggle on/off for multiple power outlets
  - **Scenes**: Execute predefined scenes for quick automation

- **Real-time Status**: Monitor device status with visual indicators for:
  - Connection status to Home Assistant
  - Device online/offline status
  - Current device state (on/off, brightness, speed)

- **Responsive UI**: Adapts to different screen sizes with:
  - Dynamic circular widgets for visual feedback
  - Intuitive controls for each device type
  - Grid or column layout based on screen width

- **WebSocket Communication**: Direct communication with Home Assistant via WebSocket API for:
  - Real-time state updates
  - Command execution
  - Authentication

- **Analytics & Monitoring**: Comprehensive tracking with Firebase for:
  - Performance metrics
  - Error reporting
  - Usage analytics
  - WebSocket communication logs

## Technical Architecture

### State Management
- Uses Riverpod for state management
- Implements the Provider pattern for dependency injection
- Utilizes Freezed for immutable state models

### Communication
- WebSocket connection to Home Assistant
- Firebase integration for analytics and monitoring
- Automatic reconnection handling

### UI Components
- Custom circular widgets with painters for visual feedback
- Responsive layout adapting to different screen sizes
- Device-specific control panels

## Getting Started

### Prerequisites
- Flutter SDK (3.x or higher)
- Dart SDK (3.x or higher)
- Home Assistant instance (accessible via WebSocket)
- Firebase project (for analytics and monitoring)

### Installation

1. Clone the repository:
```bash
git clone https://github.com/schnellenergy/schnell-home-automation.git
cd schnell-home-automation
```

2. Install dependencies:
```bash
flutter pub get
```

3. Configure Firebase:
```bash
dart pub global activate flutterfire_cli
flutterfire configure
```

4. Update Home Assistant connection details:
Edit `lib/services/home_assistant_service.dart` to update the WebSocket URL and access token:
```dart
static const String _webSocketUrl = 'ws://your-home-assistant-ip:8123/api/websocket';
static const String _accessToken = 'your-long-lived-access-token';
```

5. Run the application:
```bash
flutter run
```

## Configuration

### Home Assistant Entity IDs
The app uses predefined entity IDs for devices. You can update these in `lib/models/entity_ids.dart`:

```dart
const EntityIds({
  this.power1 = "switch.your_power_switch_1",
  this.power2 = "switch.your_power_switch_2",
  this.light = "light.your_light",
  this.fan = "fan.your_fan",
  this.favourite1 = "light.your_scene_1",
  this.favourite2 = "light.your_scene_2",
});
```

### Firebase Integration
The app uses Firebase for analytics, performance monitoring, and crash reporting. Make sure to:
1. Create a Firebase project
2. Run `flutterfire configure` to generate the necessary configuration files
3. Enable the required Firebase services (Analytics, Crashlytics, Performance)

## Development

### Project Structure
- `lib/main.dart` - Application entry point
- `lib/screens/` - Application screens
- `lib/widgets/` - Reusable UI components
- `lib/models/` - Data models
- `lib/providers/` - Riverpod providers
- `lib/services/` - Business logic and external services

### Key Components
- `HomeAssistantService` - Handles communication with Home Assistant
- `DeviceStateNotifier` - Manages device state
- `VirtualDockScreen` - Main UI screen
- `CombinedControlPanel` - Dynamic control panel for selected device

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- [Home Assistant](https://www.home-assistant.io/) for the open-source home automation platform
- [Flutter](https://flutter.dev/) for the UI framework
- [Riverpod](https://riverpod.dev/) for state management
- [Firebase](https://firebase.google.com/) for analytics and monitoring
