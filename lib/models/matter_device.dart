class MatterDevice {
  final String entityId;
  final String name;
  final String deviceType;
  final int? nodeId;
  final String? shareCode;
  final bool isCommissionedToChipTool;
  final List<MatterEndpoint> endpoints;

  const MatterDevice({
    required this.entityId,
    required this.name,
    required this.deviceType,
    this.nodeId,
    this.shareCode,
    this.isCommissionedToChipTool = false,
    this.endpoints = const [],
  });

  MatterDevice copyWith({
    String? entityId,
    String? name,
    String? deviceType,
    int? nodeId,
    String? shareCode,
    bool? isCommissionedToChipTool,
    List<MatterEndpoint>? endpoints,
  }) {
    return MatterDevice(
      entityId: entityId ?? this.entityId,
      name: name ?? this.name,
      deviceType: deviceType ?? this.deviceType,
      nodeId: nodeId ?? this.nodeId,
      shareCode: shareCode ?? this.shareCode,
      isCommissionedToChipTool: isCommissionedToChipTool ?? this.isCommissionedToChipTool,
      endpoints: endpoints ?? this.endpoints,
    );
  }

  bool get canBeController => deviceType == 'switch' || deviceType == 'dimmer_switch';
  bool get canBeControlled => deviceType == 'light' || deviceType == 'fan' || deviceType == 'switch';

  @override
  String toString() {
    return 'MatterDevice(entityId: $entityId, name: $name, deviceType: $deviceType, nodeId: $nodeId, isCommissionedToChipTool: $isCommissionedToChipTool)';
  }
}

class MatterEndpoint {
  final int endpointId;
  final List<int> supportedClusters;

  const MatterEndpoint({
    required this.endpointId,
    required this.supportedClusters,
  });

  @override
  String toString() {
    return 'MatterEndpoint(endpointId: $endpointId, supportedClusters: $supportedClusters)';
  }
}

class MatterBinding {
  final String id;
  final MatterDevice controllerDevice;
  final MatterDevice controlledDevice;
  final int controllerEndpoint;
  final int controlledEndpoint;
  final int clusterId;
  final DateTime createdAt;
  final bool isActive;

  const MatterBinding({
    required this.id,
    required this.controllerDevice,
    required this.controlledDevice,
    required this.controllerEndpoint,
    required this.controlledEndpoint,
    required this.clusterId,
    required this.createdAt,
    this.isActive = true,
  });

  MatterBinding copyWith({
    String? id,
    MatterDevice? controllerDevice,
    MatterDevice? controlledDevice,
    int? controllerEndpoint,
    int? controlledEndpoint,
    int? clusterId,
    DateTime? createdAt,
    bool? isActive,
  }) {
    return MatterBinding(
      id: id ?? this.id,
      controllerDevice: controllerDevice ?? this.controllerDevice,
      controlledDevice: controlledDevice ?? this.controlledDevice,
      controllerEndpoint: controllerEndpoint ?? this.controllerEndpoint,
      controlledEndpoint: controlledEndpoint ?? this.controlledEndpoint,
      clusterId: clusterId ?? this.clusterId,
      createdAt: createdAt ?? this.createdAt,
      isActive: isActive ?? this.isActive,
    );
  }

  @override
  String toString() {
    return 'MatterBinding(id: $id, controller: ${controllerDevice.name}, controlled: ${controlledDevice.name}, active: $isActive)';
  }
}

// Matter cluster IDs for common device types
class MatterClusters {
  static const int onOff = 6;
  static const int levelControl = 8;
  static const int colorControl = 768;
  static const int fanControl = 514;
  static const int binding = 30;
  static const int accessControl = 31;
}

// Common Matter device types
class MatterDeviceTypes {
  static const String light = 'light';
  static const String switch_ = 'switch';
  static const String dimmerSwitch = 'dimmer_switch';
  static const String fan = 'fan';
  static const String sensor = 'sensor';
}
