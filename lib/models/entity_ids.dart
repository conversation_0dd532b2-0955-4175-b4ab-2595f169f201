// Class to hold all entity IDs used in the application
class EntityIds {
  final String power1;
  final String power2;
  final String light;
  final String fan;
  final String favourite1;
  final String favourite2;

  const EntityIds({
    this.power1 = "switch.test_product_switch_1",
    this.power2 = "switch.test_product_switch_2",
    this.light = "light.test_product_5",
    this.fan = "fan.test_product",
    this.favourite1 = "light.sl_sample_light_5_2",
    this.favourite2 = "light.sl_sample_light_6_2",
  });

  // Default instance for easy access
  static const EntityIds defaultIds = EntityIds();
}


still the fan control is not working properly, especially the 2nd level of speed is not working i have checked the same fan controls with mode using the post man client and it was working as expected. so instead of the changing the fan level using percentage we can use the mode to control the fan speed.
i have mentioned the fan modes below based on the percentage. you can consider using the same, if the fan speed level is one then the fan mode is 'low'. if the fan speed is 2 then the fan mode is 'medium' and the fan speed is 3 then the fan mode is 'high' and four is 'auto'. i have mentioned the websocket payload below. you need to update the same in the home_assistant_service.dart file. the ids should auto increment same as the previous approch.


Fan control:
For 25%
{
    "type": "call_service",
    "domain": "fan",
    "service": "set_preset_mode",
    "return_response": false,
    "service_data": {
        "entity_id": "fan.test_product",
        "preset_mode": "low"
    },
    "id": 63
}

For 50%
{
    "type": "call_service",
    "domain": "fan",
    "service": "set_preset_mode",
    "return_response": false,
    "service_data": {
        "entity_id": "fan.test_product",
        "preset_mode": "medium"
    },
    "id": 64
}

For 75%
{
    "type": "call_service",
    "domain": "fan",
    "service": "set_preset_mode",
    "return_response": false,
    "service_data": {
        "entity_id": "fan.test_product",
        "preset_mode": "high"
    },
    "id": 66
}

For 100%
{
    "type": "call_service",
    "domain": "fan",
    "service": "set_preset_mode",
    "return_response": false,
    "service_data": {
        "entity_id": "fan.test_product",
        "preset_mode": "auto"
    },
    "id": 68
}

//and also i have changed the payload for the SCENE 1 and SCENE 2. please update the same in the home_assistant_service.dart file. the ids should auto increment same as the previous approch.

//SCENE - OFF button (as per the virual dock page)
{
    "type": "call_service",
    "domain": "scene",
    "service": "turn_on",
    "return_response": false,
    "service_data": {
        "entity_id": "scene.turn_off"
    },
    "id": 84
}

//SCENE - ON button (as per the virual dock page)
{
    "type": "call_service",
    "domain": "scene",
    "service": "turn_on",
    "return_response": false,
    "service_data": {
        "entity_id": "scene.turn_off_all_devices_duplicate"
    },
    "id": 82
}