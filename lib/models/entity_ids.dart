// Class to hold all entity IDs used in the application
class EntityIds {
  final String power1;
  final String power2;
  final String light;
  final String fan;
  final String favourite1;
  final String favourite2;

  const EntityIds({
    this.power1 = "switch.test_product_switch_1",
    this.power2 = "switch.test_product_switch_2",
    this.light = "light.test_product_5",
    this.fan = "fan.test_product",
    this.favourite1 = "light.sl_sample_light_5_2",
    this.favourite2 = "light.sl_sample_light_6_2",
  });

  // Default instance for easy access
  static const EntityIds defaultIds = EntityIds();
}
