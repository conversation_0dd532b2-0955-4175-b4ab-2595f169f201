// Removed freezed annotations and auto-generated parts
// import 'package:freezed_annotation/freezed_annotation.dart';
// part 'device_state.freezed.dart';
// part 'device_state.g.dart';

class DeviceState {
  // Fan state
  final bool isFanOn;
  final int fanLevel;
  final String fanMode;
  final bool isFanOnline;

  // Light state
  final bool isLightOn;
  final double brightness;
  final bool isLightOnline;

  // Power states
  final bool isPower1On;
  final bool isPower2On;
  final bool isPower1Online;
  final bool isPower2Online;

  // Favorite states
  final bool isFavourite1On;
  final bool isFavourite2On;
  final bool isFavourite1Online;
  final bool isFavourite2Online;

  // WebSocket connection state
  final bool isConnected;

  const DeviceState({
    // Fan state
    this.isFanOn = false,
    this.fanLevel = 1,
    this.fanMode = 'Normal',
    this.isFanOnline = true,

    // Light state
    this.isLightOn = false,
    this.brightness = 0.5,
    this.isLightOnline = true,

    // Power states
    this.isPower1On = false,
    this.isPower2On = false,
    this.isPower1Online = true,
    this.isPower2Online = true,

    // Favorite states
    this.isFavourite1On = false,
    this.isFavourite2On = false,
    this.isFavourite1Online = true,
    this.isFavourite2Online = true,

    // WebSocket connection state
    this.isConnected = false,
  });

  DeviceState copyWith({
    bool? isFanOn,
    int? fanLevel,
    String? fanMode,
    bool? isFanOnline,
    bool? isLightOn,
    double? brightness,
    bool? isLightOnline,
    bool? isPower1On,
    bool? isPower2On,
    bool? isPower1Online,
    bool? isPower2Online,
    bool? isFavourite1On,
    bool? isFavourite2On,
    bool? isFavourite1Online,
    bool? isFavourite2Online,
    bool? isConnected,
  }) {
    return DeviceState(
      isFanOn: isFanOn ?? this.isFanOn,
      fanLevel: fanLevel ?? this.fanLevel,
      fanMode: fanMode ?? this.fanMode,
      isFanOnline: isFanOnline ?? this.isFanOnline,
      isLightOn: isLightOn ?? this.isLightOn,
      brightness: brightness ?? this.brightness,
      isLightOnline: isLightOnline ?? this.isLightOnline,
      isPower1On: isPower1On ?? this.isPower1On,
      isPower2On: isPower2On ?? this.isPower2On,
      isPower1Online: isPower1Online ?? this.isPower1Online,
      isPower2Online: isPower2Online ?? this.isPower2Online,
      isFavourite1On: isFavourite1On ?? this.isFavourite1On,
      isFavourite2On: isFavourite2On ?? this.isFavourite2On,
      isFavourite1Online: isFavourite1Online ?? this.isFavourite1Online,
      isFavourite2Online: isFavourite2Online ?? this.isFavourite2Online,
      isConnected: isConnected ?? this.isConnected,
    );
  }
}
