import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/device_state_provider.dart';
import '../models/device_type.dart';
import '../providers/selected_device_provider.dart';
import '../services/analytics_service.dart';

class CommandInterpreter {
  final WidgetRef ref;
  final AnalyticsService _analytics = AnalyticsService();

  CommandInterpreter(this.ref);

  // Process a voice command
  void processCommand(String command, {BuildContext? context}) {
    log('Processing command: $command');
    final lowerCommand = command.toLowerCase();

    // Try to identify the command type and parameters
    bool commandProcessed = false;
    String interpretedCommand = '';

    // Light commands
    if (_matchCommand(lowerCommand, ['turn on light', 'lights on', 'light on'])) {
      interpretedCommand = 'turn_on_light';
      _turnOnLight();
      commandProcessed = true;
    } else if (_matchCommand(lowerCommand, ['turn off light', 'lights off', 'light off'])) {
      interpretedCommand = 'turn_off_light';
      _turnOffLight();
      commandProcessed = true;
    } else if (lowerCommand.contains('brightness') ||
               lowerCommand.contains('dim') ||
               (lowerCommand.contains('light') && _extractNumber(lowerCommand) != null)) {
      final number = _extractNumber(lowerCommand);
      interpretedCommand = 'set_light_brightness_${number ?? 0}';
      _setBrightness(lowerCommand);
      commandProcessed = true;
    }

    // Fan commands
    else if (_matchCommand(lowerCommand, ['turn on fan', 'fan on'])) {
      interpretedCommand = 'turn_on_fan';
      _turnOnFan();
      commandProcessed = true;
    } else if (_matchCommand(lowerCommand, ['turn off fan', 'fan off'])) {
      interpretedCommand = 'turn_off_fan';
      _turnOffFan();
      commandProcessed = true;
    } else if (lowerCommand.contains('fan level') ||
               lowerCommand.contains('fan speed') ||
               (lowerCommand.contains('fan') && _extractNumber(lowerCommand) != null)) {
      final number = _extractNumber(lowerCommand);
      interpretedCommand = 'set_fan_level_${number ?? 0}';
      _setFanLevel(lowerCommand);
      commandProcessed = true;
    }

    // Power commands
    else if (_matchCommand(lowerCommand, ['turn on power one', 'power one on', 'turn on power 1', 'power 1 on', 'first power on'])) {
      interpretedCommand = 'turn_on_power_1';
      _togglePower1(true);
      commandProcessed = true;
    } else if (_matchCommand(lowerCommand, ['turn off power one', 'power one off', 'turn off power 1', 'power 1 off', 'first power off'])) {
      interpretedCommand = 'turn_off_power_1';
      _togglePower1(false);
      commandProcessed = true;
    } else if (_matchCommand(lowerCommand, ['turn on power two', 'power two on', 'turn on power 2', 'power 2 on', 'second power on'])) {
      interpretedCommand = 'turn_on_power_2';
      _togglePower2(true);
      commandProcessed = true;
    } else if (_matchCommand(lowerCommand, ['turn off power two', 'power two off', 'turn off power 2', 'power 2 off', 'second power off'])) {
      interpretedCommand = 'turn_off_power_2';
      _togglePower2(false);
      commandProcessed = true;
    }

    // Scene commands
    else if (_matchCommand(lowerCommand, ['activate scene off', 'scene off', 'favorite one', 'favourite one', 'first scene', 'scene 1'])) {
      interpretedCommand = 'activate_scene_1';
      _activateScene(1);
      commandProcessed = true;
    } else if (_matchCommand(lowerCommand, ['activate scene on', 'scene on', 'favorite two', 'favourite two', 'second scene', 'scene 2'])) {
      interpretedCommand = 'activate_scene_2';
      _activateScene(2);
      commandProcessed = true;
    }

    // If no command was processed, try to infer the command type from the content
    if (!commandProcessed) {
      // Try to infer light commands
      if (lowerCommand.contains('light')) {
        if (lowerCommand.contains('on')) {
          interpretedCommand = 'inferred_turn_on_light';
          _turnOnLight();
          commandProcessed = true;
        } else if (lowerCommand.contains('off')) {
          interpretedCommand = 'inferred_turn_off_light';
          _turnOffLight();
          commandProcessed = true;
        }
      }

      // Try to infer fan commands
      else if (lowerCommand.contains('fan')) {
        if (lowerCommand.contains('on')) {
          interpretedCommand = 'inferred_turn_on_fan';
          _turnOnFan();
          commandProcessed = true;
        } else if (lowerCommand.contains('off')) {
          interpretedCommand = 'inferred_turn_off_fan';
          _turnOffFan();
          commandProcessed = true;
        }
      }

      // Try to infer power commands
      else if (lowerCommand.contains('power')) {
        if (lowerCommand.contains('1') || lowerCommand.contains('one') || lowerCommand.contains('first')) {
          if (lowerCommand.contains('on')) {
            interpretedCommand = 'inferred_turn_on_power_1';
            _togglePower1(true);
            commandProcessed = true;
          } else if (lowerCommand.contains('off')) {
            interpretedCommand = 'inferred_turn_off_power_1';
            _togglePower1(false);
            commandProcessed = true;
          }
        } else if (lowerCommand.contains('2') || lowerCommand.contains('two') || lowerCommand.contains('second')) {
          if (lowerCommand.contains('on')) {
            interpretedCommand = 'inferred_turn_on_power_2';
            _togglePower2(true);
            commandProcessed = true;
          } else if (lowerCommand.contains('off')) {
            interpretedCommand = 'inferred_turn_off_power_2';
            _togglePower2(false);
            commandProcessed = true;
          }
        }
      }

      // Try to infer scene commands
      else if (lowerCommand.contains('scene') || lowerCommand.contains('favorite') || lowerCommand.contains('favourite')) {
        if (lowerCommand.contains('1') || lowerCommand.contains('one') || lowerCommand.contains('first') || lowerCommand.contains('off')) {
          interpretedCommand = 'inferred_activate_scene_1';
          _activateScene(1);
          commandProcessed = true;
        } else if (lowerCommand.contains('2') || lowerCommand.contains('two') || lowerCommand.contains('second') || lowerCommand.contains('on')) {
          interpretedCommand = 'inferred_activate_scene_2';
          _activateScene(2);
          commandProcessed = true;
        }
      }
    }

    // Track the voice command
    _analytics.trackVoiceCommand(
      rawCommand: command,
      interpretedCommand: interpretedCommand,
      isRecognized: commandProcessed,
      isExecuted: commandProcessed,
    );

    // Unknown command
    if (!commandProcessed) {
      log('Unknown command: $command');
      _showFeedback(context, 'Unknown command: $command', isError: true);
    } else {
      _showFeedback(context, 'Command processed: $command');
    }
  }

  // Check if the command matches any of the patterns
  bool _matchCommand(String command, List<String> patterns) {
    return patterns.any((pattern) => command.contains(pattern));
  }

  // Extract a number from a command
  int? _extractNumber(String command) {
    // Try to find numbers in various formats
    final patterns = [
      // Look for "to X" pattern (e.g., "set brightness to 50")
      RegExp(r'to\s+(\d+)'),
      // Look for "level X" pattern (e.g., "fan level 3")
      RegExp(r'level\s+(\d+)'),
      // Look for "X percent" pattern (e.g., "50 percent")
      RegExp(r'(\d+)\s+percent'),
      // Look for any number as a fallback
      RegExp(r'(\d+)'),
    ];

    for (final pattern in patterns) {
      final match = pattern.firstMatch(command);
      if (match != null) {
        final number = int.tryParse(match.group(1)!);
        log('Extracted number: $number from command: $command using pattern: ${pattern.pattern}');
        return number;
      }
    }

    log('No number found in command: $command');
    return null;
  }

  // Show feedback to the user
  void _showFeedback(BuildContext? context, String message, {bool isError = false}) {
    if (context != null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: isError ? Colors.red : Colors.green,
          duration: const Duration(seconds: 2),
        ),
      );
    }
    log(message);
  }

  // Light control commands
  void _turnOnLight() {
    log('Turning on light');
    final notifier = ref.read(deviceStateNotifierProvider.notifier);
    ref.read(selectedDeviceProvider.notifier).state = DeviceType.light;
    notifier.toggleLight();
  }

  void _turnOffLight() {
    log('Turning off light');
    final deviceState = ref.read(deviceStateNotifierProvider);
    final notifier = ref.read(deviceStateNotifierProvider.notifier);

    if (deviceState.isLightOn) {
      ref.read(selectedDeviceProvider.notifier).state = DeviceType.light;
      notifier.toggleLight();
    }
  }

  void _setBrightness(String command) {
    final number = _extractNumber(command);
    if (number != null && number >= 0 && number <= 100) {
      log('Setting brightness to $number%');
      final notifier = ref.read(deviceStateNotifierProvider.notifier);
      ref.read(selectedDeviceProvider.notifier).state = DeviceType.light;
      notifier.updateBrightness(number / 100.0);
    } else {
      log('Invalid brightness value in command: $command');
    }
  }

  // Fan control commands
  void _turnOnFan() {
    log('Turning on fan');
    final deviceState = ref.read(deviceStateNotifierProvider);
    final notifier = ref.read(deviceStateNotifierProvider.notifier);

    if (!deviceState.isFanOn) {
      ref.read(selectedDeviceProvider.notifier).state = DeviceType.fan;
      notifier.toggleFan();
    }
  }

  void _turnOffFan() {
    log('Turning off fan');
    final deviceState = ref.read(deviceStateNotifierProvider);
    final notifier = ref.read(deviceStateNotifierProvider.notifier);

    if (deviceState.isFanOn) {
      ref.read(selectedDeviceProvider.notifier).state = DeviceType.fan;
      notifier.toggleFan();
    }
  }

  void _setFanLevel(String command) {
    final number = _extractNumber(command);
    if (number != null && number >= 0 && number <= 4) {
      log('Setting fan level to $number');
      final notifier = ref.read(deviceStateNotifierProvider.notifier);
      ref.read(selectedDeviceProvider.notifier).state = DeviceType.fan;
      notifier.updateFanSpeed(number);
    } else {
      log('Invalid fan level in command: $command');
    }
  }

  // Power control commands
  void _togglePower1(bool turnOn) {
    log('${turnOn ? "Turning on" : "Turning off"} power 1');
    final deviceState = ref.read(deviceStateNotifierProvider);
    final notifier = ref.read(deviceStateNotifierProvider.notifier);

    if (turnOn != deviceState.isPower1On) {
      ref.read(selectedDeviceProvider.notifier).state = DeviceType.power;
      notifier.togglePower1();
    }
  }

  void _togglePower2(bool turnOn) {
    log('${turnOn ? "Turning on" : "Turning off"} power 2');
    final deviceState = ref.read(deviceStateNotifierProvider);
    final notifier = ref.read(deviceStateNotifierProvider.notifier);

    if (turnOn != deviceState.isPower2On) {
      ref.read(selectedDeviceProvider.notifier).state = 'power2';
      notifier.togglePower2();
    }
  }

  // Scene activation commands
  void _activateScene(int sceneNumber) {
    log('Activating scene $sceneNumber');
    final notifier = ref.read(deviceStateNotifierProvider.notifier);

    if (sceneNumber == 1) {
      ref.read(selectedDeviceProvider.notifier).state = 'favourite1';
      notifier.executeFavouriteAction(1);
    } else if (sceneNumber == 2) {
      ref.read(selectedDeviceProvider.notifier).state = 'favourite2';
      notifier.executeFavouriteAction(2);
    }
  }
}
