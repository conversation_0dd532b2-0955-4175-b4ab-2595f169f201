import 'dart:async';
import 'dart:developer';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'analytics_service.dart';

final connectivityServiceProvider = Provider<ConnectivityService>((ref) {
  return ConnectivityService();
});

final connectivityStatusProvider = StreamProvider<bool>((ref) {
  final service = ref.watch(connectivityServiceProvider);
  return service.connectivityStream;
});

class ConnectivityService {
  static final ConnectivityService _instance = ConnectivityService._internal();
  factory ConnectivityService() => _instance;
  ConnectivityService._internal();

  final Connectivity _connectivity = Connectivity();
  final StreamController<bool> _connectivityController = StreamController<bool>.broadcast();
  StreamSubscription<ConnectivityResult>? _connectivitySubscription;
  bool _isInitialized = false;
  bool _lastStatus = false;
  final AnalyticsService _analyticsService = AnalyticsService();

  Stream<bool> get connectivityStream => _connectivityController.stream;

  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Check initial connectivity
      final initialResult = await _connectivity.checkConnectivity();
      _updateConnectionStatus(initialResult);

      // Listen for connectivity changes
      _connectivitySubscription = _connectivity.onConnectivityChanged.listen(_updateConnectionStatus);

      _isInitialized = true;
      log('Connectivity service initialized');
    } catch (e) {
      log('Error initializing connectivity service: $e');
      _analyticsService.trackError(
        errorType: 'connectivity_service_init_error',
        errorMessage: e.toString(),
        stackTrace: StackTrace.current.toString(),
      );
    }
  }

  void _updateConnectionStatus(ConnectivityResult result) {
    final isConnected = result != ConnectivityResult.none;
    
    // Only emit and log if the status has changed
    if (isConnected != _lastStatus) {
      _lastStatus = isConnected;
      _connectivityController.add(isConnected);
      
      log('Connectivity changed: ${isConnected ? 'Connected' : 'Disconnected'}');
      
      // Track the connectivity change
      _analyticsService.trackPerformanceMetric(
        metricType: 'connectivity_change',
        value: isConnected ? 1.0 : 0.0,
        context: {'connectivity_type': result.name},
      );
    }
  }

  void dispose() {
    _connectivitySubscription?.cancel();
    _connectivityController.close();
  }
}
