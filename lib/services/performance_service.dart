import 'dart:developer';

import 'package:firebase_performance/firebase_performance.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'analytics_service.dart';

final performanceServiceProvider = Provider<PerformanceService>((ref) {
  return PerformanceService();
});

class PerformanceService {
  static final PerformanceService _instance = PerformanceService._internal();
  factory PerformanceService() => _instance;
  PerformanceService._internal();

  final FirebasePerformance _performance = FirebasePerformance.instance;
  final AnalyticsService _analyticsService = AnalyticsService();
  final Map<String, Trace> _activeTraces = {};
  bool _isInitialized = false;

  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Enable performance collection
      await _performance.setPerformanceCollectionEnabled(true);

      _isInitialized = true;
      log('Performance service initialized');
    } catch (e) {
      log('Error initializing performance service: $e');
      _analyticsService.trackError(
        errorType: 'performance_service_init_error',
        errorMessage: e.toString(),
        stackTrace: StackTrace.current.toString(),
      );
    }
  }

  // Start a trace for measuring a specific operation
  void startTrace(String traceName) {
    if (_activeTraces.containsKey(traceName)) {
      log('Warning: Trace "$traceName" is already running');
      return;
    }

    try {
      final trace = _performance.newTrace(traceName);
      trace.start();
      _activeTraces[traceName] = trace;
      log('Started trace: $traceName');
    } catch (e) {
      log('Error starting trace $traceName: $e');
      _analyticsService.trackError(
        errorType: 'performance_trace_start_error',
        errorMessage: 'Failed to start trace $traceName: $e',
        stackTrace: StackTrace.current.toString(),
      );
    }
  }

  // Stop a trace and record its metrics
  Future<void> stopTrace(String traceName) async {
    final trace = _activeTraces[traceName];
    if (trace == null) {
      log('Warning: Tried to stop non-existent trace "$traceName"');
      return;
    }

    try {
      final startTime = DateTime.now();
      await trace.stop();
      final endTime = DateTime.now();
      final durationMs = endTime.difference(startTime).inMicroseconds;
      final durationSeconds = durationMs / 1000000;

      log('Stopped trace: $traceName (duration: ${durationSeconds.toStringAsFixed(2)}s)');

      // Also log to our analytics service
      _analyticsService.trackPerformanceMetric(
        metricType: 'trace_duration',
        value: durationSeconds,
        context: {'trace_name': traceName},
      );

      _activeTraces.remove(traceName);
    } catch (e) {
      log('Error stopping trace $traceName: $e');
      _analyticsService.trackError(
        errorType: 'performance_trace_stop_error',
        errorMessage: 'Failed to stop trace $traceName: $e',
        stackTrace: StackTrace.current.toString(),
      );
    }
  }

  // Add a metric to a running trace
  void incrementMetric(String traceName, String metricName, int incrementBy) {
    final trace = _activeTraces[traceName];
    if (trace == null) {
      log('Warning: Tried to increment metric on non-existent trace "$traceName"');
      return;
    }

    try {
      trace.incrementMetric(metricName, incrementBy);
      log('Incremented metric $metricName on trace $traceName by $incrementBy');
    } catch (e) {
      log('Error incrementing metric $metricName on trace $traceName: $e');
      _analyticsService.trackError(
        errorType: 'performance_metric_increment_error',
        errorMessage: 'Failed to increment metric $metricName: $e',
        stackTrace: StackTrace.current.toString(),
      );
    }
  }

  // Track HTTP/HTTPS network request
  Future<void> trackNetworkRequest({
    required String url,
    required HttpMethod method,
    int? responseCode,
    int? requestPayloadSize,
    int? responsePayloadSize,
    String? contentType,
    int? durationMs,
  }) async {
    try {
      final isSuccess = responseCode != null && responseCode >= 200 && responseCode < 300;

      // Log to our analytics service
      _analyticsService.trackPerformanceMetric(
        metricType: 'network_request',
        value: isSuccess ? 1.0 : 0.0,
        context: {
          'url': url,
          'method': method.name,
          'response_code': responseCode,
          'request_size': requestPayloadSize,
          'response_size': responsePayloadSize,
          'duration_ms': durationMs,
        },
      );

      log('Tracked network request for ${method.name} $url');
    } catch (e) {
      log('Error tracking network request: $e');
      _analyticsService.trackError(
        errorType: 'performance_network_track_error',
        errorMessage: 'Failed to track network request: $e',
        stackTrace: StackTrace.current.toString(),
        context: {'url': url, 'method': method.name},
      );
    }
  }
}
