import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'package:http/http.dart' as http;
import '../models/matter_device.dart';

class MatterBindingService {
  static const String _chipToolBaseUrl = 'http://homeassistant.local:8123/77f1785d_chip_tool/ingress';
  static const String _chipToolApiUrl = '$_chipToolBaseUrl/api';
  
  final List<MatterDevice> _devices = [];
  final List<MatterBinding> _bindings = [];
  final StreamController<List<MatterDevice>> _devicesController = StreamController.broadcast();
  final StreamController<List<MatterBinding>> _bindingsController = StreamController.broadcast();

  Stream<List<MatterDevice>> get devicesStream => _devicesController.stream;
  Stream<List<MatterBinding>> get bindingsStream => _bindingsController.stream;
  
  List<MatterDevice> get devices => List.unmodifiable(_devices);
  List<MatterBinding> get bindings => List.unmodifiable(_bindings);

  // Initialize with default Matter devices from Home Assistant
  void initializeDevices() {
    _devices.clear();
    
    // Add default devices based on entity IDs
    _devices.addAll([
      const MatterDevice(
        entityId: 'fan.test_product',
        name: 'Matter Fan',
        deviceType: MatterDeviceTypes.fan,
        endpoints: [MatterEndpoint(endpointId: 1, supportedClusters: [MatterClusters.onOff, MatterClusters.fanControl])],
      ),
      const MatterDevice(
        entityId: 'light.test_product_5',
        name: 'Matter Light',
        deviceType: MatterDeviceTypes.light,
        endpoints: [MatterEndpoint(endpointId: 1, supportedClusters: [MatterClusters.onOff, MatterClusters.levelControl])],
      ),
      const MatterDevice(
        entityId: 'switch.test_product_switch_1',
        name: 'Matter Switch 1',
        deviceType: MatterDeviceTypes.switch_,
        endpoints: [MatterEndpoint(endpointId: 1, supportedClusters: [MatterClusters.onOff])],
      ),
      const MatterDevice(
        entityId: 'switch.test_product_switch_2',
        name: 'Matter Switch 2',
        deviceType: MatterDeviceTypes.switch_,
        endpoints: [MatterEndpoint(endpointId: 1, supportedClusters: [MatterClusters.onOff])],
      ),
    ]);
    
    _devicesController.add(_devices);
    log('Initialized ${_devices.length} Matter devices');
  }

  // Commission device to CHIPTool using share code
  Future<bool> commissionDeviceToChipTool(String deviceEntityId, String shareCode, int nodeId) async {
    try {
      log('Commissioning device $deviceEntityId to CHIPTool with node ID $nodeId');
      
      // Execute CHIPTool pairing command
      final success = await _executeChipToolCommand([
        'pairing',
        'code',
        nodeId.toString(),
        shareCode,
      ]);
      
      if (success) {
        // Update device with node ID and commission status
        final deviceIndex = _devices.indexWhere((d) => d.entityId == deviceEntityId);
        if (deviceIndex != -1) {
          _devices[deviceIndex] = _devices[deviceIndex].copyWith(
            nodeId: nodeId,
            shareCode: shareCode,
            isCommissionedToChipTool: true,
          );
          _devicesController.add(_devices);
          log('Successfully commissioned device $deviceEntityId to CHIPTool');
        }
      }
      
      return success;
    } catch (e) {
      log('Error commissioning device to CHIPTool: $e');
      return false;
    }
  }

  // Create binding between two Matter devices
  Future<bool> createBinding(
    MatterDevice controllerDevice,
    MatterDevice controlledDevice,
    int controllerEndpoint,
    int controlledEndpoint,
    int clusterId,
  ) async {
    try {
      if (controllerDevice.nodeId == null || controlledDevice.nodeId == null) {
        throw Exception('Both devices must be commissioned to CHIPTool first');
      }

      log('Creating binding: ${controllerDevice.name} -> ${controlledDevice.name}');

      // Step 1: Set up access control on the controlled device
      final aclSuccess = await _setupAccessControl(
        controlledDevice.nodeId!,
        controllerDevice.nodeId!,
      );
      
      if (!aclSuccess) {
        throw Exception('Failed to set up access control');
      }

      // Step 2: Create the binding on the controller device
      final bindingSuccess = await _createDeviceBinding(
        controllerDevice.nodeId!,
        controllerEndpoint,
        controlledDevice.nodeId!,
        controlledEndpoint,
        clusterId,
      );

      if (bindingSuccess) {
        // Add binding to our list
        final binding = MatterBinding(
          id: '${controllerDevice.entityId}_${controlledDevice.entityId}_${DateTime.now().millisecondsSinceEpoch}',
          controllerDevice: controllerDevice,
          controlledDevice: controlledDevice,
          controllerEndpoint: controllerEndpoint,
          controlledEndpoint: controlledEndpoint,
          clusterId: clusterId,
          createdAt: DateTime.now(),
        );
        
        _bindings.add(binding);
        _bindingsController.add(_bindings);
        
        log('Successfully created binding: ${binding.id}');
        return true;
      }

      return false;
    } catch (e) {
      log('Error creating binding: $e');
      return false;
    }
  }

  // Set up access control list on the controlled device
  Future<bool> _setupAccessControl(int controlledNodeId, int controllerNodeId) async {
    try {
      log('Setting up access control for node $controlledNodeId, allowing controller $controllerNodeId');
      
      final aclData = [
        {
          "fabricIndex": 1,
          "privilege": 5,
          "authMode": 2,
          "subjects": null,
          "targets": null
        },
        {
          "fabricIndex": 1,
          "privilege": 3,
          "authMode": 2,
          "subjects": [controllerNodeId],
          "targets": null
        }
      ];

      return await _executeChipToolCommand([
        'accesscontrol',
        'write',
        'acl',
        jsonEncode(aclData),
        controlledNodeId.toString(),
        '0',
      ]);
    } catch (e) {
      log('Error setting up access control: $e');
      return false;
    }
  }

  // Create binding on the controller device
  Future<bool> _createDeviceBinding(
    int controllerNodeId,
    int controllerEndpoint,
    int controlledNodeId,
    int controlledEndpoint,
    int clusterId,
  ) async {
    try {
      log('Creating binding on controller node $controllerNodeId endpoint $controllerEndpoint');
      
      final bindingData = [
        {
          "node": controlledNodeId,
          "endpoint": controlledEndpoint,
          "cluster": clusterId
        }
      ];

      return await _executeChipToolCommand([
        'binding',
        'write',
        'binding',
        jsonEncode(bindingData),
        controllerNodeId.toString(),
        controllerEndpoint.toString(),
      ]);
    } catch (e) {
      log('Error creating device binding: $e');
      return false;
    }
  }

  // Execute CHIPTool command via HTTP API
  Future<bool> _executeChipToolCommand(List<String> args) async {
    try {
      final command = 'chip-tool ${args.join(' ')}';
      log('Executing CHIPTool command: $command');

      final response = await http.post(
        Uri.parse('$_chipToolApiUrl/execute'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'command': command,
          'timeout': 30,
        }),
      );

      if (response.statusCode == 200) {
        final result = jsonDecode(response.body);
        final success = result['success'] ?? false;
        
        if (success) {
          log('CHIPTool command executed successfully');
        } else {
          log('CHIPTool command failed: ${result['error']}');
        }
        
        return success;
      } else {
        log('HTTP request failed with status: ${response.statusCode}');
        return false;
      }
    } catch (e) {
      log('Error executing CHIPTool command: $e');
      return false;
    }
  }

  // Remove binding
  Future<bool> removeBinding(String bindingId) async {
    try {
      final bindingIndex = _bindings.indexWhere((b) => b.id == bindingId);
      if (bindingIndex == -1) {
        return false;
      }

      final binding = _bindings[bindingIndex];
      
      // Clear binding on controller device
      final success = await _executeChipToolCommand([
        'binding',
        'write',
        'binding',
        '[]', // Empty binding list
        binding.controllerDevice.nodeId.toString(),
        binding.controllerEndpoint.toString(),
      ]);

      if (success) {
        _bindings.removeAt(bindingIndex);
        _bindingsController.add(_bindings);
        log('Successfully removed binding: $bindingId');
      }

      return success;
    } catch (e) {
      log('Error removing binding: $e');
      return false;
    }
  }

  // Get available controller devices
  List<MatterDevice> getControllerDevices() {
    return _devices.where((device) => 
      device.canBeController && device.isCommissionedToChipTool
    ).toList();
  }

  // Get available controlled devices
  List<MatterDevice> getControlledDevices() {
    return _devices.where((device) => 
      device.canBeControlled && device.isCommissionedToChipTool
    ).toList();
  }

  void dispose() {
    _devicesController.close();
    _bindingsController.close();
  }
}
