import 'dart:async';
import 'dart:convert';
import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'analytics_service.dart';
import 'performance_service.dart';

final websocketMonitorProvider = Provider<WebSocketMonitorService>((ref) {
  return WebSocketMonitorService();
});

class WebSocketMonitorService {
  static final WebSocketMonitorService _instance = WebSocketMonitorService._internal();
  factory WebSocketMonitorService() => _instance;
  WebSocketMonitorService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAnalytics _analytics = FirebaseAnalytics.instance;
  final AnalyticsService _analyticsService = AnalyticsService();
  final PerformanceService _performanceService = PerformanceService();
  
  final StreamController<Map<String, dynamic>> _websocketLogsController = 
      StreamController<Map<String, dynamic>>.broadcast();
  
  Stream<Map<String, dynamic>> get websocketLogs => _websocketLogsController.stream;
  
  bool _isInitialized = false;

  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Set up listeners for WebSocket logs
      _setupWebSocketLogListeners();
      
      _isInitialized = true;
      log('WebSocket monitor service initialized');
    } catch (e) {
      log('Error initializing WebSocket monitor service: $e');
      _analyticsService.trackError(
        errorType: 'websocket_monitor_init_error',
        errorMessage: e.toString(),
        stackTrace: StackTrace.current.toString(),
      );
    }
  }

  void _setupWebSocketLogListeners() {
    // Listen for WebSocket logs in Firestore
    _firestore
        .collection('websocket_logs')
        .orderBy('timestamp', descending: true)
        .limit(100)
        .snapshots()
        .listen(
      (snapshot) {
        for (final change in snapshot.docChanges) {
          if (change.type == DocumentChangeType.added) {
            final log = change.doc.data()!;
            _websocketLogsController.add({
              'id': change.doc.id,
              ...log,
            });
          }
        }
      },
      onError: (error) {
        log('Error listening to WebSocket logs: $error');
        _analyticsService.trackError(
          errorType: 'websocket_log_listener_error',
          errorMessage: error.toString(),
          stackTrace: StackTrace.current.toString(),
        );
      },
    );
  }

  // Log a WebSocket message
  void logWebSocketMessage({
    required String direction, // 'incoming' or 'outgoing'
    required Map<String, dynamic> message,
    required bool isSuccess,
    String? clientId,
  }) {
    final timestamp = DateTime.now();
    
    // Start performance trace for logging
    _performanceService.startTrace('websocket_log_message');
    
    try {
      final logEntry = {
        'timestamp': timestamp.toIso8601String(),
        'direction': direction,
        'message': message,
        'is_success': isSuccess,
        'source': 'android_app',
        'client_id': clientId,
        'created_at': FieldValue.serverTimestamp(),
      };
      
      // Log to Firestore
      _firestore.collection('websocket_logs').add(logEntry).then((_) {
        log('WebSocket message logged to Firestore');
        _performanceService.stopTrace('websocket_log_message');
      }).catchError((error) {
        log('Error logging WebSocket message to Firestore: $error');
        _analyticsService.trackError(
          errorType: 'websocket_log_error',
          errorMessage: error.toString(),
          stackTrace: StackTrace.current.toString(),
          context: {'message': jsonEncode(message)},
        );
        _performanceService.stopTrace('websocket_log_message');
      });
      
      // Log to Firebase Analytics for important messages
      final messageType = message['type'] as String?;
      if (messageType == 'call_service' ||
          (messageType == 'result' && message['success'] == false)) {
        _analytics.logEvent(
          name: 'websocket_${messageType ?? 'unknown'}',
          parameters: {
            'direction': direction,
            'domain': message['domain'] ?? '',
            'service': message['service'] ?? '',
            'entity_id': message['service_data']?['entity_id'] ?? '',
            'is_success': isSuccess,
          },
        );
      }
      
      // Also log to the analytics service
      _analyticsService.trackWebSocketMessage(
        direction: direction,
        message: message,
        clientId: clientId,
      );
      
    } catch (e) {
      log('Error logging WebSocket message: $e');
      _analyticsService.trackError(
        errorType: 'websocket_log_error',
        errorMessage: e.toString(),
        stackTrace: StackTrace.current.toString(),
      );
      _performanceService.stopTrace('websocket_log_message');
    }
  }

  // Get WebSocket logs from Firestore
  Future<List<Map<String, dynamic>>> getWebSocketLogs({
    int limit = 100,
    String? eventType,
    DateTime? startTime,
    DateTime? endTime,
  }) async {
    try {
      Query query = _firestore.collection('websocket_logs')
          .orderBy('timestamp', descending: true)
          .limit(limit);
      
      if (eventType != null) {
        query = query.where('message.type', isEqualTo: eventType);
      }
      
      if (startTime != null) {
        query = query.where('timestamp', isGreaterThanOrEqualTo: startTime.toIso8601String());
      }
      
      if (endTime != null) {
        query = query.where('timestamp', isLessThanOrEqualTo: endTime.toIso8601String());
      }
      
      final snapshot = await query.get();
      
      final logs = <Map<String, dynamic>>[];
      for (final doc in snapshot.docs) {
        logs.add({
          'id': doc.id,
          ...doc.data() as Map<String, dynamic>,
        });
      }
      
      return logs;
    } catch (e) {
      log('Error getting WebSocket logs: $e');
      _analyticsService.trackError(
        errorType: 'get_websocket_logs_error',
        errorMessage: e.toString(),
        stackTrace: StackTrace.current.toString(),
      );
      return [];
    }
  }

  void dispose() {
    _websocketLogsController.close();
  }
}
