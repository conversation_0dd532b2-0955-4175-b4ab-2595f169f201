import 'dart:async';
import 'dart:convert';
import 'dart:developer';

import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'analytics_service.dart';
import 'performance_service.dart';

final websocketMonitorProvider = Provider<WebSocketMonitorService>((ref) {
  return WebSocketMonitorService();
});

/// Temporarily disabled for iOS build - Firebase dependencies commented out
/// This is a stub implementation that logs events locally without Firebase
class WebSocketMonitorService {
  static final WebSocketMonitorService _instance = WebSocketMonitorService._internal();
  factory WebSocketMonitorService() => _instance;
  WebSocketMonitorService._internal();

  final AnalyticsService _analyticsService = AnalyticsService();
  final PerformanceService _performanceService = PerformanceService();

  final StreamController<Map<String, dynamic>> _websocketLogsController =
      StreamController<Map<String, dynamic>>.broadcast();

  Stream<Map<String, dynamic>> get websocketLogsStream =>
      _websocketLogsController.stream;

  void initialize() {
    log(
      'WebSocket Monitor Service initialized (stub - Firebase disabled for iOS build)',
    );
  }

  void logMessage({
    required String direction,
    required Map<String, dynamic> message,
    required bool isSuccess,
    String? clientId,
  }) {
    log(
      'WebSocket Monitor: $direction - ${message['type']} (success: $isSuccess)',
    );

    // Track with analytics service
    _analyticsService.trackWebSocketMessage(
      direction: direction,
      message: message,
      clientId: clientId,
    );
  }

  Future<List<Map<String, dynamic>>> getRecentLogs({
    int limit = 50,
    DateTime? startTime,
    DateTime? endTime,
  }) async {
    log('WebSocket Monitor: getRecentLogs called (stub implementation)');
    return []; // Return empty list in stub implementation
  }

  void dispose() {
    _websocketLogsController.close();
  }
}
