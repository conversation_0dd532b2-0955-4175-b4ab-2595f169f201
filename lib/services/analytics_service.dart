import 'dart:developer';

/// A service for collecting and sending analytics data to the admin portal
/// Temporarily disabled for iOS build - Firebase dependencies commented out
/// This is a stub implementation that logs events locally without Firebase
class AnalyticsService {

  // Singleton instance
  static final AnalyticsService _instance = AnalyticsService._internal();
  factory AnalyticsService() => _instance;
  AnalyticsService._internal();

  // Initialize the service (stub implementation)
  Future<void> initialize() async {
    log(
      'Analytics service initialized (stub - Firebase disabled for iOS build)',
    );
  }

  // Stub methods for compatibility
  void trackDeviceCommand({
    required String deviceType,
    required String entityId,
    required String command,
    required bool isSuccess,
    Map<String, dynamic>? parameters,
  }) {
    log(
      'Analytics: Device command - $deviceType.$command ($entityId) - Success: $isSuccess',
    );
  }

  void trackError({
    required String errorType,
    required String errorMessage,
    String? stackTrace,
    Map<String, dynamic>? context,
  }) {
    log('Analytics: Error - $errorType: $errorMessage');
  }

  void trackAppEvent({
    required String eventType,
    Map<String, dynamic>? parameters,
  }) {
    log('Analytics: App event - $eventType');
  }

  void trackWebSocketMessage({
    required String direction,
    required Map<String, dynamic> message,
    String? clientId,
  }) {
    log('Analytics: WebSocket $direction - ${message['type']}');
  }

  void trackVoiceCommand({
    required String rawCommand,
    required String interpretedCommand,
    required bool isRecognized,
    Map<String, dynamic>? context,
  }) {
    log(
      'Analytics: Voice command - "$rawCommand" -> "$interpretedCommand" (recognized: $isRecognized)',
    );
  }

  void trackPerformanceMetric({
    required String metricType,
    required double value,
    Map<String, dynamic>? context,
  }) {
    log('Analytics: Performance metric - $metricType: $value');
  }
}
