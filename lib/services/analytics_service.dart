import 'dart:developer';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:device_info_plus/device_info_plus.dart';

/// A service for collecting and sending analytics data to the admin portal
class AnalyticsService {

  // Singleton instance
  static final AnalyticsService _instance = AnalyticsService._internal();
  factory AnalyticsService() => _instance;
  AnalyticsService._internal();

  // Queue for storing events when offline
  final List<Map<String, dynamic>> _eventQueue = [];
  bool _isSending = false;

  // Device information
  String? _deviceId;
  String? _deviceModel;
  String? _osVersion;
  String? _appVersion;

  // Firebase instances
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAnalytics _analytics = FirebaseAnalytics.instance;
  final FirebaseCrashlytics _crashlytics = FirebaseCrashlytics.instance;

  // Initialize the service
  Future<void> initialize() async {
    try {
      // Get device information
      final deviceInfo = DeviceInfoPlugin();
      if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        _deviceId = androidInfo.id;
        _deviceModel = androidInfo.model;
        _osVersion = androidInfo.version.release;
        _appVersion = '1.0.0'; // This should come from your app's version
      } else {
        _deviceId = 'unknown';
        _deviceModel = 'unknown';
        _osVersion = 'unknown';
        _appVersion = '1.0.0';
      }

      // Set user properties in Firebase Analytics
      await _analytics.setUserId(id: _deviceId);
      await _analytics.setUserProperty(name: 'device_model', value: _deviceModel);
      await _analytics.setUserProperty(name: 'os_version', value: _osVersion);

      // Set user ID in Crashlytics
      await _crashlytics.setUserIdentifier(_deviceId ?? 'unknown');

      // Process any queued events
      _processQueue();

      log('Analytics service initialized with device ID: $_deviceId');
    } catch (e) {
      log('Error initializing analytics service: $e');
      _crashlytics.recordError(e, StackTrace.current);
    }
  }

  // Track a device command
  void trackDeviceCommand({
    required String deviceType,
    required String entityId,
    required String command,
    required bool isSuccess,
    Map<String, dynamic>? parameters,
  }) {
    final event = {
      'type': 'device_command',
      'timestamp': DateTime.now().toIso8601String(),
      'device_type': deviceType,
      'entity_id': entityId,
      'command': command,
      'is_success': isSuccess,
      'parameters': parameters ?? {},
      'source': 'android_app',
      'device_info': _getDeviceInfo(),
    };

    _logEvent(event);
  }

  // Track a voice command
  void trackVoiceCommand({
    required String rawCommand,
    required String interpretedCommand,
    required bool isRecognized,
    required bool isExecuted,
  }) {
    final event = {
      'type': 'voice_command',
      'timestamp': DateTime.now().toIso8601String(),
      'raw_command': rawCommand,
      'interpreted_command': interpretedCommand,
      'is_recognized': isRecognized,
      'is_executed': isExecuted,
      'device_info': _getDeviceInfo(),
    };

    _logEvent(event);
  }

  // Track app session
  void trackAppSession({
    required bool isStart,
    int? durationSeconds,
  }) {
    final event = {
      'type': isStart ? 'session_start' : 'session_end',
      'timestamp': DateTime.now().toIso8601String(),
      'duration_seconds': durationSeconds,
      'device_info': _getDeviceInfo(),
    };

    _logEvent(event);
  }

  // Track device connectivity
  void trackDeviceConnectivity({
    required String deviceType,
    required String entityId,
    required bool isOnline,
  }) {
    final event = {
      'type': 'device_connectivity',
      'timestamp': DateTime.now().toIso8601String(),
      'device_type': deviceType,
      'entity_id': entityId,
      'is_online': isOnline,
      'device_info': _getDeviceInfo(),
    };

    _logEvent(event);
  }

  // Track errors
  void trackError({
    required String errorType,
    required String errorMessage,
    String? stackTrace,
    Map<String, dynamic>? context,
  }) {
    final event = {
      'type': 'error',
      'timestamp': DateTime.now().toIso8601String(),
      'error_type': errorType,
      'error_message': errorMessage,
      'stack_trace': stackTrace,
      'context': context ?? {},
      'source': 'android_app',
      'device_info': _getDeviceInfo(),
    };

    _logEvent(event);
  }

  // Track user feedback
  void trackFeedback({
    required String feedbackType,
    required String message,
    int? rating,
    Map<String, dynamic>? additionalData,
  }) {
    final event = {
      'type': 'feedback',
      'timestamp': DateTime.now().toIso8601String(),
      'feedback_type': feedbackType,
      'message': message,
      'rating': rating,
      'additional_data': additionalData ?? {},
      'device_info': _getDeviceInfo(),
    };

    _logEvent(event);
  }

  // Track performance metrics
  void trackPerformanceMetric({
    required String metricType,
    required double value,
    Map<String, dynamic>? context,
  }) {
    final event = {
      'type': 'performance_metric',
      'timestamp': DateTime.now().toIso8601String(),
      'metric_type': metricType,
      'value': value,
      'context': context ?? {},
      'source': 'android_app', // Explicitly mark the source
      'device_info': _getDeviceInfo(),
    };

    _logEvent(event);
  }

  // Track WebSocket message
  void trackWebSocketMessage({
    required String direction, // 'incoming' or 'outgoing'
    required Map<String, dynamic> message,
    String? clientId,
  }) {
    final event = {
      'timestamp': DateTime.now().toIso8601String(),
      'type': 'websocket',
      'direction': direction,
      'message': message,
      'source': 'android_app',
      'client_id': clientId ?? _deviceId,
      'device_info': _getDeviceInfo(),
    };

    // Log to Firestore directly to match the admin portal format
    try {
      _firestore.collection('websocket_logs').add({
        ...event,
        'created_at': FieldValue.serverTimestamp(),
      });
    } catch (error) {
      log('Error logging WebSocket message: $error');
    }

    // Also log to Firebase Analytics for important messages
    final messageType = message['type'] as String?;
    if (messageType == 'call_service' ||
        (messageType == 'result' && message['success'] == false)) {
      _analytics.logEvent(
        name: 'websocket_${messageType ?? 'unknown'}',
        parameters: {
          'direction': direction,
          'domain': message['domain'] ?? '',
          'service': message['service'] ?? '',
          'entity_id': message['service_data']?['entity_id'] ?? '',
        },
      );
    }
  }

  // Get device information
  Map<String, dynamic> _getDeviceInfo() {
    return {
      'device_id': _deviceId ?? 'unknown',
      'device_model': _deviceModel ?? 'unknown',
      'os_version': _osVersion ?? 'unknown',
      'app_version': _appVersion ?? 'unknown',
      'platform': kIsWeb ? 'web' : Platform.operatingSystem,
    };
  }

  // Log an event (add to queue and try to send)
  void _logEvent(Map<String, dynamic> event) {
    _eventQueue.add(event);
    log('Analytics event queued: ${event['type']}');

    // Log event to Firebase Analytics
    _analytics.logEvent(
      name: 'app_${event['type']}',
      parameters: {
        'device_type': event['device_type'] ?? '',
        'entity_id': event['entity_id'] ?? '',
        'command': event['command'] ?? '',
        'is_success': event['is_success'] != null ? (event['is_success'] ? '1' : '0') : '0',
      },
    );

    // If it's an error event, also log to Crashlytics
    if (event['type'] == 'error') {
      _crashlytics.recordError(
        event['error_message'] ?? 'Unknown error',
        StackTrace.fromString(event['stack_trace'] ?? ''),
        reason: event['error_type'],
        information: [event['context'] ?? {}],
      );
    }

    // Process the queue to send to Firestore
    _processQueue();
  }

  // Process the event queue
  Future<void> _processQueue() async {
    if (_isSending || _eventQueue.isEmpty) return;

    _isSending = true;
    try {
      // Take up to 10 events from the queue
      final batch = _eventQueue.take(10).toList();
      final success = await _sendEvents(batch);

      if (success) {
        // Remove sent events from the queue
        _eventQueue.removeRange(0, batch.length);
        log('Analytics batch sent successfully (${batch.length} events)');

        // If there are more events, process them
        if (_eventQueue.isNotEmpty) {
          _processQueue();
        }
      } else {
        log('Failed to send analytics batch, will retry later');
      }
    } catch (e) {
      log('Error processing analytics queue: $e');
      _crashlytics.recordError(e, StackTrace.current);
    } finally {
      _isSending = false;
    }
  }

  // Send events to Firestore
  Future<bool> _sendEvents(List<Map<String, dynamic>> events) async {
    try {
      // Create a batch write
      final batch = FirebaseFirestore.instance.batch();

      for (final event in events) {
        // Create a reference to a new document
        final collectionName = '${event['type']}s'; // e.g., 'device_commands', 'errors'
        final docRef = FirebaseFirestore.instance.collection(collectionName).doc();

        // Add the document to the batch
        batch.set(docRef, {
          ...event,
          'created_at': FieldValue.serverTimestamp(),
        });
      }

      // Commit the batch
      await batch.commit();

      return true;
    } catch (e) {
      log('Error sending analytics events: $e');
      _crashlytics.recordError(e, StackTrace.current);
      return false;
    }
  }
}
