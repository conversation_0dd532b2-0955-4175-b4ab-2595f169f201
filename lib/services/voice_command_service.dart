import 'dart:async';
import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:speech_to_text/speech_to_text.dart';
import 'package:speech_to_text/speech_recognition_result.dart';
import 'package:permission_handler/permission_handler.dart';

/// A service that handles voice commands using speech recognition
class VoiceCommandService {
  final SpeechToText _speech = SpeechToText();
  bool _isListening = false;
  String _lastRecognizedWords = '';
  final StreamController<String> _commandController = StreamController<String>.broadcast();

  // Stream of recognized commands
  Stream<String> get commandStream => _commandController.stream;

  // Current listening status
  bool get isListening => _isListening;

  // Last recognized words
  String get lastRecognizedWords => _lastRecognizedWords;

  // Initialize the speech recognition service
  Future<bool> initialize() async {
    log('Initializing speech recognition service');

    // Request microphone permission
    final status = await Permission.microphone.request();
    if (status != PermissionStatus.granted) {
      log('Microphone permission denied');
      return false;
    }

    // Initialize speech recognition
    final isInitialized = await _speech.initialize(
      onError: (error) => log('Speech recognition error: $error'),
      onStatus: (status) => log('Speech recognition status: $status'),
    );

    log('Speech recognition initialized: $isInitialized');
    return isInitialized;
  }

  // Start listening for voice commands
  Future<void> startListening(BuildContext context) async {
    log('Starting to listen for voice commands');

    if (!_speech.isAvailable) {
      final isInitialized = await initialize();
      if (!isInitialized) {
        log('Could not initialize speech recognition');
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Speech recognition not available')),
          );
        }
        return;
      }
    }

    if (_speech.isListening) {
      log('Already listening');
      return;
    }

    _isListening = true;

    // Show a snackbar to indicate listening has started
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Listening for commands...')),
      );
    }

    await _speech.listen(
      onResult: _onSpeechResult,
      listenFor: const Duration(seconds: 5),  // Listen for 5 seconds only
      pauseFor: const Duration(seconds: 2),
      localeId: 'en_US',
      listenOptions: SpeechListenOptions(
        partialResults: true,
        cancelOnError: true,
        listenMode: ListenMode.confirmation,
      ),
    );

    // Set a timer to stop listening after 5 seconds
    Future.delayed(const Duration(seconds: 5), () {
      if (_speech.isListening) {
        log('Stopping listening after 5 seconds timeout');
        stopListening();
      }
    });
  }

  // Handle speech recognition results
  void _onSpeechResult(SpeechRecognitionResult result) {
    log('Speech result: ${result.recognizedWords}');
    _lastRecognizedWords = result.recognizedWords;

    // Only process final results
    if (result.finalResult) {
      log('Final speech result: ${result.recognizedWords}');

      // Make sure we have a non-empty command
      if (result.recognizedWords.isNotEmpty) {
        // Add the command to the stream for processing
        _commandController.add(result.recognizedWords);
        log('Command added to stream: ${result.recognizedWords}');
      } else {
        log('Empty command, not processing');
      }

      // Update listening state
      _isListening = false;
    }
  }

  // Stop listening for voice commands
  Future<void> stopListening() async {
    log('Stopping listening for voice commands');
    if (_speech.isListening) {
      await _speech.stop();
    }
    _isListening = false;
  }

  // Dispose resources
  void dispose() {
    _speech.stop();
    _commandController.close();
  }
}
