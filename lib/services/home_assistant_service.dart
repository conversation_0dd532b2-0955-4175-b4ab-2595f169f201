import 'dart:async';
import 'dart:convert';
import 'dart:developer';

import 'package:web_socket_channel/web_socket_channel.dart';
// Commented out Firebase analytics services
// import 'analytics_service.dart';
// import 'performance_service.dart';
// import 'websocket_monitor_service.dart';

class HomeAssistantService {
  // Using IP address instead of hostname for better reliability
  static const String _webSocketUrl = 'ws://*************:8123/api/websocket';
  static const String _accessToken =
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiI5ZjVlM2QyNjQ4ZjQ0NDAwOWE0NWExNjJlMDAwZmRjZSIsImlhdCI6MTczODgxODgwNSwiZXhwIjoyMDU0MTc4ODA1fQ.vOEYd3rL0poOHWUOkf0_QTwxpkzDTtubCzpvlb9zWcY';

  WebSocketChannel? _channel;
  bool _isConnected = false;
  int _messageId = 1;

  // Services - Commented out Firebase analytics services
  // final AnalyticsService _analyticsService = AnalyticsService();
  // final PerformanceService _performanceService = PerformanceService();
  // final WebSocketMonitorService _websocketMonitorService =
  //     WebSocketMonitorService();

  final StreamController<Map<String, dynamic>> _stateChangesController =
      StreamController<Map<String, dynamic>>.broadcast();

  Stream<Map<String, dynamic>> get stateChanges =>
      _stateChangesController.stream;
  bool get isConnected => _isConnected;

  // Reconnection parameters
  int _reconnectAttempts = 0;
  static const int _maxReconnectAttempts = 5;
  static const Duration _initialReconnectDelay = Duration(seconds: 2);

  // Performance tracking - Commented out unused fields
  // DateTime? _lastMessageSentTime;
  final Map<int, DateTime> _pendingResponses = {};

  Future<void> connect() async {
    if (_isConnected) return;

    final connectionStartTime = DateTime.now();

    try {
      log('Connecting to WebSocket at $_webSocketUrl');

      _channel = WebSocketChannel.connect(Uri.parse(_webSocketUrl));

      // Send authentication message
      final authMessage = {"type": "auth", "access_token": _accessToken};

      _channel!.sink.add(jsonEncode(authMessage));
      log('Sent authentication message');

      // Track connection attempt - Commented out Firebase analytics
      // _analyticsService.trackDeviceConnectivity(
      //   deviceType: 'home_assistant',
      //   entityId: 'websocket',
      //   isOnline: true,
      // );

      // Track connection attempt with timestamp - Commented out Firebase analytics
      // _analyticsService.trackPerformanceMetric(
      //   metricType: 'websocket_connection_attempt',
      //   value: 0,
      //   context: {
      //     'url': _webSocketUrl,
      //     'timestamp': connectionStartTime.toIso8601String(),
      //   },
      // );

      _channel!.stream.listen(
        (message) {
          final receiveTime = DateTime.now();
          log('Received: $message');

          try {
            final data = jsonDecode(message);

            // Calculate response time for pending requests - Commented out unused variables
            if (data['id'] != null &&
                _pendingResponses.containsKey(data['id'])) {
              // final requestTime = _pendingResponses[data['id']]!;
              // final responseTime =
              //     receiveTime.difference(requestTime).inMilliseconds;
              // final isSuccess = data['success'] ?? true;

              // Track response time in analytics - Commented out Firebase analytics
              // _analyticsService.trackPerformanceMetric(
              //   metricType: 'websocket_response_time',
              //   value: responseTime.toDouble(),
              //   context: {
              //     'message_id': data['id'],
              //     'message_type': data['type'],
              //     'success': isSuccess,
              //   },
              // );

              // Stop the performance trace for this call - Commented out Firebase analytics
              // _performanceService.stopTrace('websocket_call_${data['type']}');

              // Log the incoming message to WebSocket monitor - Commented out Firebase analytics
              // _websocketMonitorService.logWebSocketMessage(
              //   direction: 'incoming',
              //   message: data,
              //   isSuccess: isSuccess,
              // );

              // Remove from pending responses
              _pendingResponses.remove(data['id']);
            }

            // Handle authentication success
            if (data['type'] == 'auth_ok') {
              _isConnected = true;
              _reconnectAttempts =
                  0; // Reset reconnect attempts on successful connection

              // Calculate connection time
              final connectionTime =
                  receiveTime.difference(connectionStartTime).inMilliseconds;

              // Track successful connection with timing - Commented out Firebase analytics
              // _analyticsService.trackPerformanceMetric(
              //   metricType: 'websocket_connection_success',
              //   value: connectionTime.toDouble(),
              //   context: {'url': _webSocketUrl},
              // );

              log('WebSocket connected successfully in $connectionTime ms');

              _subscribeToEvents();

              // Track successful authentication - Commented out Firebase analytics
              // _analyticsService.trackDeviceConnectivity(
              //   deviceType: 'home_assistant',
              //   entityId: 'websocket_auth',
              //   isOnline: true,
              // );
            }

            // Handle authentication failure
            if (data['type'] == 'auth_invalid') {
              _isConnected = false;

              // Track authentication failure - Commented out Firebase analytics
              // _analyticsService.trackError(
              //   errorType: 'WebSocketAuthenticationError',
              //   errorMessage: 'Authentication failed: ${data["message"]}',
              // );

              log('Authentication failed: ${data["message"]}');

              // Don't reconnect immediately to avoid infinite loop
              Future.delayed(const Duration(minutes: 1), () {
                connect();
              });

              return;
            }

            // Handle state changes
            if (data['type'] == 'event' && data['event'] != null) {
              final event = data['event'];
              if (event['event_type'] == 'state_changed') {
                _stateChangesController.add(event['data']);

                // Track state change - Commented out unused variables
                // final entityId = event['data']['entity_id'];
                // final newState = event['data']['new_state']['state'];

                // Track state change - Commented out Firebase analytics
                // _analyticsService.trackDeviceConnectivity(
                //   deviceType: entityId.split('.')[0],
                //   entityId: entityId,
                //   isOnline: newState != 'unavailable' && newState != 'unknown',
                // );
              }
            }

            // Handle result messages
            if (data['type'] == 'result') {
              final success = data['success'] ?? false;

              if (!success) {
                // Track error response - Commented out Firebase analytics
                // _analyticsService.trackError(
                //   errorType: 'WebSocketCommandError',
                //   errorMessage:
                //       'Command failed: ${data["error"]?["message"] ?? "Unknown error"}',
                //   context: {
                //     'message_id': data['id'],
                //     'error_code': data['error']?['code'],
                //   },
                // );
              }
            }
          } catch (e) {
            log('Error processing WebSocket message: $e');

            // Track message processing error - Commented out Firebase analytics
            // _analyticsService.trackError(
            //   errorType: 'WebSocketMessageProcessingError',
            //   errorMessage: 'Error processing message: $e',
            //   context: {'raw_message': message.toString()},
            // );
          }
        },
        onError: (error) {
          log('WebSocket Error: $error');
          _isConnected = false;

          // Track connection error - Commented out Firebase analytics
          // _analyticsService.trackError(
          //   errorType: 'WebSocketConnectionError',
          //   errorMessage: 'WebSocket Error: $error',
          // );

          _reconnect();
        },
        onDone: () {
          log('WebSocket Connection Closed');
          _isConnected = false;

          // Track connection closed - Commented out Firebase analytics
          // _analyticsService.trackDeviceConnectivity(
          //   deviceType: 'home_assistant',
          //   entityId: 'websocket',
          //   isOnline: false,
          // );

          _reconnect();
        },
      );
    } catch (e) {
      log('Error connecting to WebSocket: $e');
      _isConnected = false;

      // Track connection error - Commented out Firebase analytics
      // _analyticsService.trackError(
      //   errorType: 'WebSocketConnectionError',
      //   errorMessage: 'Error connecting to WebSocket: $e',
      // );

      _reconnect();
    }
  }

  // Reconnect to WebSocket with exponential backoff
  void _reconnect() {
    _reconnectAttempts++;

    if (_reconnectAttempts > _maxReconnectAttempts) {
      log('Maximum reconnection attempts reached. Giving up.');

      // Track max reconnect attempts reached - Commented out Firebase analytics
      // _analyticsService.trackError(
      //   errorType: 'WebSocketMaxReconnectAttemptsReached',
      //   errorMessage: 'Maximum reconnection attempts reached',
      //   context: {
      //     'attempts': _reconnectAttempts,
      //     'max_attempts': _maxReconnectAttempts,
      //   },
      // );

      return;
    }

    // Calculate delay with exponential backoff
    final delay = Duration(
      milliseconds:
          _initialReconnectDelay.inMilliseconds *
          (1 << (_reconnectAttempts - 1)),
    );

    log(
      'Reconnecting to WebSocket in ${delay.inSeconds} seconds (attempt $_reconnectAttempts of $_maxReconnectAttempts)',
    );

    // Track reconnect attempt - Commented out Firebase analytics
    // _analyticsService.trackPerformanceMetric(
    //   metricType: 'websocket_reconnect_attempt',
    //   value: _reconnectAttempts.toDouble(),
    //   context: {
    //     'delay_ms': delay.inMilliseconds,
    //     'max_attempts': _maxReconnectAttempts,
    //   },
    // );

    Future.delayed(delay, () {
      if (!_isConnected) {
        connect();
      }
    });
  }

  void _subscribeToEvents() {
    final subscribeMessage = {
      "id": _messageId++,
      "type": "subscribe_events",
      "event_type": "state_changed",
    };
    sendMessage(subscribeMessage);
  }

  void sendMessage(Map<String, dynamic> message) {
    if (!_isConnected || _channel == null) {
      log('WebSocket not connected. Attempting to reconnect...');
      connect();
      return;
    }

    // Commented out unused variable: final sendTime = DateTime.now();

    try {
      // Store send time for tracking response time - Commented out since analytics disabled
      if (message['id'] != null) {
        _pendingResponses[message['id']] = DateTime.now();
      }

      // Start performance trace for WebSocket call - Commented out Firebase analytics
      // _performanceService.startTrace('websocket_call_${message['type']}');

      // Log the outgoing message to both services - Commented out Firebase analytics
      // _analyticsService.trackWebSocketMessage(
      //   direction: 'outgoing', // From us to Home Assistant
      //   message: message,
      // );

      // Also log to WebSocket monitor service - Commented out Firebase analytics
      // _websocketMonitorService.logWebSocketMessage(
      //   direction: 'outgoing',
      //   message: message,
      //   isSuccess: true,
      // );

      _channel!.sink.add(jsonEncode(message));

      log('Sent message: ${jsonEncode(message)}');

      // Track the WebSocket call - Commented out Firebase analytics
      // if (message['type'] == 'call_service') {
      //   _analyticsService.trackDeviceCommand(
      //     deviceType: message['domain'] ?? 'unknown',
      //     entityId: message['service_data']?['entity_id'] ?? 'unknown',
      //     command: message['service'] ?? 'unknown',
      //     isSuccess: true, // Assume success, errors will be caught elsewhere
      //     parameters: message['service_data'],
      //   );
      // }
    } catch (e) {
      log('Error sending message: $e');

      // Stop the performance trace for this call - Commented out Firebase analytics
      // _performanceService.stopTrace('websocket_call_${message['type']}');

      // Track the error in analytics - Commented out Firebase analytics
      // _analyticsService.trackError(
      //   errorType: 'WebSocketSendError',
      //   errorMessage: 'Failed to send WebSocket message: $e',
      //   context: {
      //     'message_type': message['type'],
      //     'message_id': message['id'],
      //     'domain': message['domain'],
      //     'service': message['service'],
      //   },
      // );

      // Log the failed message to WebSocket monitor - Commented out Firebase analytics
      // _websocketMonitorService.logWebSocketMessage(
      //   direction: 'outgoing',
      //   message: message,
      //   isSuccess: false,
      // );

      // Remove from pending responses if there was an error
      if (message['id'] != null) {
        _pendingResponses.remove(message['id']);
      }

      // Try to reconnect
      _reconnect();
    }
  }

  // Fan control methods
  void toggleFan(String entityId, bool isOn, int fanLevel) {
    if (isOn) {
      // Map fan levels 1-4 to specific percentages
      int percentage;
      switch (fanLevel) {
        case 1:
          percentage = 25;
          break;
        case 2:
          percentage = 50;
          break;
        case 3:
          percentage = 75;
          break;
        case 4:
          percentage = 100;
          break;
        default:
          percentage = 25; // Default to lowest speed if invalid level
      }

      // First set the percentage
      final percentageMessage = {
        "type": "call_service",
        "domain": "fan",
        "service": "set_percentage",
        "return_response": false,
        "service_data": {"entity_id": entityId, "percentage": percentage},
        "id": _messageId++,
      };
      sendMessage(percentageMessage);

      // Then turn on the fan
      final turnOnMessage = {
        "type": "call_service",
        "domain": "fan",
        "service": "turn_on",
        "return_response": false,
        "service_data": {"entity_id": entityId},
        "id": _messageId++,
      };
      sendMessage(turnOnMessage);
    } else {
      // Turn off the fan
      final message = {
        "type": "call_service",
        "domain": "fan",
        "service": "turn_off",
        "return_response": false,
        "service_data": {"entity_id": entityId},
        "id": _messageId++,
      };
      sendMessage(message);
    }
  }

  void setFanSpeed(String entityId, int level) {
    if (level == 0) {
      // Turn off the fan if level is 0
      final message = {
        "type": "call_service",
        "domain": "fan",
        "service": "turn_off",
        "return_response": false,
        "service_data": {"entity_id": entityId},
        "id": _messageId++,
      };
      sendMessage(message);
    } else {
      // Map fan levels 1-4 to specific percentages
      int percentage;
      switch (level) {
        case 1:
          percentage = 25;
          break;
        case 2:
          percentage = 50;
          break;
        case 3:
          percentage = 75;
          break;
        case 4:
          percentage = 100;
          break;
        default:
          percentage = 25; // Default to lowest speed if invalid level
      }

      // Set the fan percentage for levels 1-4
      final message = {
        "type": "call_service",
        "domain": "fan",
        "service": "set_percentage",
        "return_response": false,
        "service_data": {"entity_id": entityId, "percentage": percentage},
        "id": _messageId++,
      };
      sendMessage(message);
    }
  }

  // Light control methods
  void toggleLight(String entityId, bool isOn) {
    final service = isOn ? "turn_on" : "turn_off";
    log('Toggling light $entityId to ${isOn ? "ON" : "OFF"}');

    final message = {
      "type": "call_service",
      "domain": "light",
      "service": service,
      "return_response": false,
      "service_data": {"entity_id": entityId},
      "id": _messageId++,
    };

    log('Sending light toggle message: ${jsonEncode(message)}');
    sendMessage(message);
  }

  void addDeviceOnNetwork(String passcode) {
    log('Adding device $passcode');

    final message = {
      "pin": int.parse(passcode),
      "id": _messageId++,
      "type": "matter/commission_on_network",
    };

    log('Sending add device message: ${jsonEncode(message)}');
    sendMessage(message);
  }

  void setBrightness(String entityId, double value) {
    log('Setting brightness for $entityId to ${(value * 100).round()}%');

    final message = {
      "type": "call_service",
      "domain": "light",
      "service": "turn_on",
      "return_response": false,
      "service_data": {
        "entity_id": entityId,
        "brightness_pct": (value * 100).round(),
      },
      "id": _messageId++,
    };

    log('Sending brightness message: ${jsonEncode(message)}');
    sendMessage(message);
  }

  // Power control methods
  void togglePower(String entityId, bool isOn) {
    final service = isOn ? "turn_on" : "turn_off";

    final message = {
      "type": "call_service",
      "domain": "switch",
      "service": service,
      "return_response": false,
      "service_data": {"entity_id": entityId},
      "id": _messageId++,
    };
    sendMessage(message);
  }

  void dispose() {
    // Log app session end - Commented out Firebase analytics
    // _analyticsService.trackAppSession(
    //   isStart: false,
    //   durationSeconds:
    //       _lastMessageSentTime != null
    //           ? DateTime.now().difference(_lastMessageSentTime!).inSeconds
    //           : null,
    // );

    // Close WebSocket connection
    _channel?.sink.close();
    _stateChangesController.close();

    // Process any pending analytics events - Commented out Firebase analytics
    // _analyticsService.trackDeviceConnectivity(
    //   deviceType: 'home_assistant',
    //   entityId: 'websocket',
    //   isOnline: false,
    // );
  }

  // Simulate a state change for testing
  void simulateStateChange(
    String entityId,
    String newState, [
    Map<String, dynamic>? attributes,
  ]) {
    // Create default attributes if none provided
    attributes ??= {};

    final data = {
      'entity_id': entityId,
      'new_state': {'state': newState, 'attributes': attributes},
    };

    _stateChangesController.add(data);
    log(
      'Simulated state change for $entityId: $newState with attributes: $attributes',
    );
  }

  // Simulate device going online/offline for testing
  void simulateDeviceAvailability(String entityId, bool isOnline) {
    final availability = isOnline ? 'online' : 'offline';
    final attributes = {'availability': availability};

    // Simulate the state change with the availability attribute
    simulateStateChange(entityId, 'on', attributes);

    log('Simulated ${isOnline ? "online" : "offline"} status for $entityId');
  }
}
