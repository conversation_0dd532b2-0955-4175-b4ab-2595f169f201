import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:homeautomation/old_pages/light_control_page.dart';
import 'package:homeautomation/providers/home_assistant_provider.dart';
import '../providers/device_state_provider.dart';
import '../providers/entity_ids_provider.dart';
import '../widgets/combined_control_panel.dart';
import '../widgets/device_grid.dart';
import 'matter_binding_screen.dart';

class VirtualDockScreen extends ConsumerStatefulWidget {
  const VirtualDockScreen({super.key});

  @override
  ConsumerState<VirtualDockScreen> createState() => _VirtualDockScreenState();
}

class _VirtualDockScreenState extends ConsumerState<VirtualDockScreen> {
  static const platform = MethodChannel(
    'com.schnell.homeautomation/commission',
  );
  @override
  void initState() {
    super.initState();
    platform.setMethodCallHandler((call) async {
      switch (call.method) {
        case 'onPasscodeReceived':

          String passcode = call.arguments;
          final service = ref.read(homeAssistantServiceProvider);
          service.addDeviceOnNetwork(passcode);
          break;
        default:
          throw MissingPluginException('Not implemented: ${call.method}');
      }
    });
  }

  // Handle device online/offline status changes
  void _handleDeviceStatusChange(String value, WidgetRef ref) {
    final service = ref.read(homeAssistantServiceProvider);
    final entityIds = ref.read(entityIdsProvider);

    switch (value) {
      case 'fan_online':
        service.simulateDeviceAvailability(entityIds.fan, true);
        break;
      case 'fan_offline':
        service.simulateDeviceAvailability(entityIds.fan, false);
        break;
      case 'light_online':
        service.simulateDeviceAvailability(entityIds.light, true);
        break;
      case 'light_offline':
        service.simulateDeviceAvailability(entityIds.light, false);
        break;
      case 'power1_online':
        service.simulateDeviceAvailability(entityIds.power1, true);
        break;
      case 'power1_offline':
        service.simulateDeviceAvailability(entityIds.power1, false);
        break;
      case 'power2_online':
        service.simulateDeviceAvailability(entityIds.power2, true);
        break;
      case 'power2_offline':
        service.simulateDeviceAvailability(entityIds.power2, false);
        break;
      case 'fav1_online':
        service.simulateDeviceAvailability(entityIds.favourite1, true);
        break;
      case 'fav1_offline':
        service.simulateDeviceAvailability(entityIds.favourite1, false);
        break;
      case 'fav2_online':
        service.simulateDeviceAvailability(entityIds.favourite2, true);
        break;
      case 'fav2_offline':
        service.simulateDeviceAvailability(entityIds.favourite2, false);
        break;
    }
  }

  Future<void> startCommissioning() async {
    try {
      final result = await platform.invokeMethod('startCommissioning');
      print(result);
    } on PlatformException catch (e) {
      print(e);
    }
  }

  @override
  Widget build(BuildContext context) {
    final deviceState = ref.watch(deviceStateNotifierProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text('Bedroom > Wall Dock - 1'),
        actions: [
          
          // Connection status button
          IconButton(
            icon: Icon(
              deviceState.isConnected
                  ? Icons.wifi
                  : Icons.devices_other_rounded,
            ),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const LightControlPage(),
                ),
              );
            },
          ),
          // Matter binding button
          IconButton(
            icon: const Icon(Icons.link),
            tooltip: 'Matter Device Binding',
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const MatterBindingScreen(),
                ),
              );
            },
          ),
          // Device online/offline status menu
          PopupMenuButton<String>(
            icon: const Icon(Icons.network_check),
            tooltip: 'Test device online/offline status',
            onSelected: (value) {
              _handleDeviceStatusChange(value, ref);
            },
            itemBuilder:
                (context) => [
                  const PopupMenuItem(
                    value: 'fan_online',
                    child: Text('Fan Online'),
                  ),
                  const PopupMenuItem(
                    value: 'fan_offline',
                    child: Text('Fan Offline'),
                  ),
                  const PopupMenuItem(
                    value: 'light_online',
                    child: Text('Light Online'),
                  ),
                  const PopupMenuItem(
                    value: 'light_offline',
                    child: Text('Light Offline'),
                  ),
                  const PopupMenuItem(
                    value: 'power1_online',
                    child: Text('Power 1 Online'),
                  ),
                  const PopupMenuItem(
                    value: 'power1_offline',
                    child: Text('Power 1 Offline'),
                  ),
                  const PopupMenuItem(
                    value: 'power2_online',
                    child: Text('Power 2 Online'),
                  ),
                  const PopupMenuItem(
                    value: 'power2_offline',
                    child: Text('Power 2 Offline'),
                  ),
                  const PopupMenuItem(
                    value: 'fav1_online',
                    child: Text('Favorite 1 Online'),
                  ),
                  const PopupMenuItem(
                    value: 'fav1_offline',
                    child: Text('Favorite 1 Offline'),
                  ),
                  const PopupMenuItem(
                    value: 'fav2_online',
                    child: Text('Favorite 2 Online'),
                  ),
                  const PopupMenuItem(
                    value: 'fav2_offline',
                    child: Text('Favorite 2 Offline'),
                  ),
                ],
          ),
        ],
      ),
      backgroundColor: Colors.grey[900],
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: LayoutBuilder(
            builder: (context, constraints) {
              return SingleChildScrollView(
                child: ConstrainedBox(
                  constraints: BoxConstraints(minHeight: constraints.maxHeight),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // Combined device control panel with dynamic circular widget
                      const CombinedControlPanel(),
                      // Device selection grid fixed at bottom
                      const DeviceGrid(),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: startCommissioning,
        tooltip: 'Add Device',
        child: const Icon(Icons.add),
      ),
    );
  }
}
