import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/device_type.dart';
import '../providers/device_state_provider.dart';
import '../providers/selected_device_provider.dart';
import 'circular_widgets/fan_circular_widget.dart';
import 'circular_widgets/light_circular_widget.dart';
import 'circular_widgets/power_circular_widget.dart';
import 'circular_widgets/favourite_circular_widget.dart';
import 'controls/fan_controls.dart';
import 'controls/light_controls.dart';
import 'controls/power_controls.dart';
import 'controls/favourite_controls.dart';

class CombinedControlPanel extends ConsumerWidget {
  const CombinedControlPanel({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final deviceState = ref.watch(deviceStateNotifierProvider);
    final selectedDevice = ref.watch(selectedDeviceProvider);

    return LayoutBuilder(
      builder: (context, constraints) {
        final maxWidth = constraints.maxWidth;

        // Calculate responsive sizes for the circular widget
        final containerHeight = maxWidth < 400 ? 250.0 : 250.0;
        final arcSize = maxWidth < 400 ? 220.0 : 220.0;
        final centerCircleSize = maxWidth < 400 ? 120.0 : 120.0;
        final fontSize = maxWidth < 400 ? 32.0 : 32.0;

        return Container(
          decoration: BoxDecoration(
            color: Colors.grey[850],
            borderRadius: BorderRadius.circular(20),
          ),
          child: Column(
            children: [
              // Dynamic circular widget
              Container(
                height: containerHeight,
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    // Arc background and progress
                    _buildDynamicCircularWidget(
                      selectedDevice,
                      arcSize,
                      centerCircleSize,
                      fontSize,
                      deviceState,
                    ),
                  ],
                ),
              ),

              // Device specific controls
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: _buildDeviceSpecificControls(
                  selectedDevice,
                  maxWidth,
                  deviceState,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildDynamicCircularWidget(
    String selectedDevice,
    double arcSize,
    double centerCircleSize,
    double fontSize,
    deviceState,
  ) {
    switch (selectedDevice) {
      case DeviceType.fan:
        return FanCircularWidget(
          arcSize: arcSize,
          centerCircleSize: centerCircleSize,
          fontSize: fontSize,
        );
      case DeviceType.light:
        return LightCircularWidget(
          arcSize: arcSize,
          centerCircleSize: centerCircleSize,
          fontSize: fontSize,
        );
      case DeviceType.power:
        return PowerCircularWidget(
          arcSize: arcSize,
          centerCircleSize: centerCircleSize,
          fontSize: fontSize,
          isPowerOn: deviceState.isPower1On,
        );
      case 'power2':
        return PowerCircularWidget(
          arcSize: arcSize,
          centerCircleSize: centerCircleSize,
          fontSize: fontSize,
          isPowerOn: deviceState.isPower2On,
        );
      case 'favourite1':
        return FavouriteCircularWidget(
          arcSize: arcSize,
          centerCircleSize: centerCircleSize,
          fontSize: fontSize,
          favouriteNumber: 1,
        );
      case 'favourite2':
        return FavouriteCircularWidget(
          arcSize: arcSize,
          centerCircleSize: centerCircleSize,
          fontSize: fontSize,
          favouriteNumber: 2,
        );
      default:
        return Container();
    }
  }

  Widget _buildDeviceSpecificControls(
    String selectedDevice,
    double maxWidth,
    deviceState,
  ) {
    switch (selectedDevice) {
      case DeviceType.fan:
        return FanControls(maxWidth: maxWidth);
      case DeviceType.light:
        return LightControls(maxWidth: maxWidth);
      case DeviceType.power:
        return PowerControls(
          maxWidth: maxWidth,
          isPowerOn: deviceState.isPower1On,
        );
      case 'power2':
        return PowerControls(
          maxWidth: maxWidth,
          isPowerOn: deviceState.isPower2On,
          isSecondPower: true,
        );
      case 'favourite1':
        return FavouriteControls(
          maxWidth: maxWidth,
          favouriteNumber: 1,
        );
      case 'favourite2':
        return FavouriteControls(
          maxWidth: maxWidth,
          favouriteNumber: 2,
        );
      default:
        return Container();
    }
  }
}
