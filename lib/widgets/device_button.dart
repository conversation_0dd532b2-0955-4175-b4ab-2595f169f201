import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/device_type.dart';
import '../providers/device_state_provider.dart';
import '../providers/selected_device_provider.dart';

class DeviceButton extends ConsumerWidget {
  final String type;
  final IconData icon;
  final String label;
  final bool isSecondPower;
  final bool isFav1;
  final bool isFav2;

  const DeviceButton({
    super.key,
    required this.type,
    required this.icon,
    required this.label,
    this.isSecondPower = false,
    this.isFav1 = false,
    this.isFav2 = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final deviceState = ref.watch(deviceStateNotifierProvider);
    final notifier = ref.read(deviceStateNotifierProvider.notifier);
    final selectedDevice = ref.watch(selectedDeviceProvider);

    bool isSelected = false;
    if (type == DeviceType.fan && selectedDevice == DeviceType.fan) {
      isSelected = true;
    } else if (type == DeviceType.light && selectedDevice == DeviceType.light) {
      isSelected = true;
    } else if (type == DeviceType.power && selectedDevice == DeviceType.power && !isSecondPower) {
      isSelected = true;
    } else if (type == DeviceType.power && selectedDevice == 'power2' && isSecondPower) {
      isSelected = true;
    } else if (type == DeviceType.favourite && selectedDevice == 'favourite1' && isFav1) {
      isSelected = true;
    } else if (type == DeviceType.favourite && selectedDevice == 'favourite2' && isFav2) {
      isSelected = true;
    }

    return Expanded(
      child: LayoutBuilder(
        builder: (context, constraints) {
          // Fixed sizes for all device cards
          final iconSize = 28.0;
          final fontSize = 14.0;
          final padding = 8.0;

          return Padding(
            padding: EdgeInsets.all(padding),
            child: InkWell(
              onTap: () {
                if (type == DeviceType.power && isSecondPower) {
                  ref.read(selectedDeviceProvider.notifier).state = 'power2';
                } else if (type == DeviceType.favourite && isFav1) {
                  ref.read(selectedDeviceProvider.notifier).state = 'favourite1';
                  // Execute favourite1 action
                  notifier.executeFavouriteAction(1);
                } else if (type == DeviceType.favourite && isFav2) {
                  ref.read(selectedDeviceProvider.notifier).state = 'favourite2';
                  // Execute favourite2 action
                  notifier.executeFavouriteAction(2);
                } else {
                  ref.read(selectedDeviceProvider.notifier).state = type;
                }
              },
              child: Container(
                height: 180,
                width: double.infinity, // Make sure the container takes full width
                decoration: BoxDecoration(
                  color: isSelected
                      ? const Color.fromARGB(255, 74, 106, 136).withAlpha(180)
                      : Colors.grey[800],
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Stack(
                  children: [
                    // Main content
                    Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(top: 4.0),
                            child: Icon(
                              icon,
                              color: Colors.white,
                              size: iconSize,
                            ),
                          ),
                          SizedBox(height: padding / 2),
                          Text(
                            label,
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: fontSize,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          if (type != DeviceType.favourite)
                            Transform.scale(
                              scale: 0.8,
                              child: Switch(
                                value: _getDeviceState(deviceState, type, isSecondPower),
                                onChanged: (_) => _toggleDevice(notifier, type, isSecondPower),
                                activeColor: Colors.blue,
                              ),
                            ),
                        ],
                      ),
                    ),

                    // Status indicator dot
                    if (type != DeviceType.favourite)
                      Positioned(
                        top: 8,
                        right: 8,
                        child: Container(
                          width: 6,
                          height: 6,
                          decoration: BoxDecoration(
                            color: !_getDeviceOnlineStatus(deviceState, type, isSecondPower, isFav1, isFav2)
                                ? Colors.red
                                : Colors.green,
                            shape: BoxShape.circle,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  bool _getDeviceState(deviceState, String type, bool isSecondPower) {
    if (type == DeviceType.fan) return deviceState.isFanOn;
    if (type == DeviceType.light) return deviceState.isLightOn;
    if (type == DeviceType.power && !isSecondPower) return deviceState.isPower1On;
    if (type == DeviceType.power && isSecondPower) return deviceState.isPower2On;
    return false;
  }

  bool _getDeviceOnlineStatus(deviceState, String type, bool isSecondPower, bool isFav1, bool isFav2) {
    if (type == DeviceType.fan) return deviceState.isFanOnline;
    if (type == DeviceType.light) return deviceState.isLightOnline;
    if (type == DeviceType.power && !isSecondPower) return deviceState.isPower1Online;
    if (type == DeviceType.power && isSecondPower) return deviceState.isPower2Online;
    if (type == DeviceType.favourite && isFav1) return deviceState.isFavourite1Online;
    if (type == DeviceType.favourite && isFav2) return deviceState.isFavourite2Online;
    return false;
  }

  void _toggleDevice(notifier, String type, bool isSecondPower) {
    if (type == DeviceType.fan) {
      notifier.toggleFan();
    } else if (type == DeviceType.light) {
      notifier.toggleLight();
    } else if (type == DeviceType.power && !isSecondPower) {
      notifier.togglePower1();
    } else if (type == DeviceType.power && isSecondPower) {
      notifier.togglePower2();
    }
  }
}
