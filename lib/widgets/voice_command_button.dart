import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/voice_command_provider.dart';
import '../services/command_interpreter.dart';

class VoiceCommandButton extends ConsumerWidget {
  const VoiceCommandButton({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isListening = ref.watch(isListeningProvider);
    final lastRecognizedWords = ref.watch(lastRecognizedWordsProvider);

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Animated mic button
        AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: isListening ? Colors.red.withValues(alpha: 51) : Colors.transparent, // alpha 51 is ~0.2 opacity
            boxShadow: isListening
                ? [
                    BoxShadow(
                      color: Colors.red.withValues(alpha: 77), // alpha 77 is ~0.3 opacity
                      blurRadius: 10,
                      spreadRadius: 5,
                    )
                  ]
                : [],
          ),
          child: FloatingActionButton(
            onPressed: () => _toggleListening(context, ref),
            backgroundColor: isListening ? Colors.red : Colors.blue,
            child: Icon(
              isListening ? Icons.mic : Icons.mic,
              color: Colors.white,
            ),
          ),
        ),

        // Recognized text display
        if (lastRecognizedWords.isNotEmpty)
          Padding(
            padding: const EdgeInsets.only(top: 8.0),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.black54,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Text(
                lastRecognizedWords,
                style: const TextStyle(color: Colors.white),
                textAlign: TextAlign.center,
              ),
            ),
          ),

        // Listening indicator
        if (isListening)
          Padding(
            padding: const EdgeInsets.only(top: 8.0),
            child: Text(
              'Listening...',
              style: TextStyle(
                color: Colors.red,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
      ],
    );
  }

  Future<void> _toggleListening(BuildContext context, WidgetRef ref) async {
    final voiceService = ref.read(voiceCommandServiceProvider);
    final isListening = ref.read(isListeningProvider);

    if (isListening) {
      await voiceService.stopListening();
      ref.read(isListeningProvider.notifier).state = false;
    } else {
      final isInitialized = await voiceService.initialize();
      if (isInitialized) {
        // Set up the command stream listener first
        // We're intentionally not storing the subscription as it will be automatically
        // disposed when the widget is disposed
        voiceService.commandStream.listen((command) {
          log('Received command from stream: $command');

          // Update the UI with the recognized command
          ref.read(lastRecognizedWordsProvider.notifier).state = command;

          // Process the command
          if (context.mounted) {
            final interpreter = CommandInterpreter(ref);
            interpreter.processCommand(command, context: context);
          }

          // Update listening state
          ref.read(isListeningProvider.notifier).state = false;
        });

        // Set listening state to true
        ref.read(isListeningProvider.notifier).state = true;

        // Start listening for commands
        if (context.mounted) {
          await voiceService.startListening(context);
        }

        // Don't cancel the subscription immediately - let it process commands
        // The subscription will be automatically disposed when the widget is disposed
      }
    }
  }
}
