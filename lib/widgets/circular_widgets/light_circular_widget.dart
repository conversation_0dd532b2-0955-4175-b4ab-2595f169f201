import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../providers/device_state_provider.dart';
import '../painters/brightness_arc_painter.dart';

class LightCircularWidget extends ConsumerWidget {
  final double arcSize;
  final double centerCircleSize;
  final double fontSize;

  const LightCircularWidget({
    super.key,
    required this.arcSize,
    required this.centerCircleSize,
    required this.fontSize,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final deviceState = ref.watch(deviceStateNotifierProvider);

    return Stack(
      alignment: Alignment.center,
      children: [
        // Brightness arc
        CustomPaint(
          size: Size(arcSize, arcSize),
          painter: <PERSON><PERSON><PERSON><PERSON><PERSON>ain<PERSON>(deviceState.brightness),
        ),

        // Center brightness display
        Container(
          width: centerCircleSize,
          height: centerCircleSize,
          decoration: BoxDecoration(
            color: deviceState.isLightOn 
                ? Colors.amber.withAlpha((deviceState.brightness * 255).round()) 
                : Colors.grey[700],
            shape: BoxShape.circle,
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                deviceState.isLightOn 
                    ? '${(deviceState.brightness * 100).round()}%' 
                    : 'OFF',
                style: TextStyle(
                  fontSize: fontSize,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
