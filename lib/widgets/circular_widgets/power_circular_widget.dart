import 'package:flutter/material.dart';
import '../painters/power_arc_painter.dart';

class PowerCircularWidget extends StatelessWidget {
  final double arcSize;
  final double centerCircleSize;
  final double fontSize;
  final bool isPowerOn;

  const PowerCircularWidget({
    super.key,
    required this.arcSize,
    required this.centerCircleSize,
    required this.fontSize,
    required this.isPowerOn,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.center,
      children: [
        // Power status arc
        CustomPaint(
          size: Si<PERSON>(arcSize, arcSize),
          painter: PowerArcPainter(isPowerOn ? 1.0 : 0.0),
        ),
        
        // Center power status display
        Container(
          width: centerCircleSize,
          height: centerCircleSize,
          decoration: BoxDecoration(
            color: isPowerOn ? Colors.green : Colors.red,
            shape: BoxShape.circle,
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                isPowerOn ? Icons.power : Icons.power_off,
                color: Colors.white,
                size: fontSize,
              ),
              Text(
                isPowerOn ? 'ON' : 'OFF',
                style: TextStyle(
                  fontSize: fontSize * 0.5,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
