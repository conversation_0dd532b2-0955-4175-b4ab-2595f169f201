import 'package:flutter/material.dart';

class FavouriteCircularWidget extends StatelessWidget {
  final double arcSize;
  final double centerCircleSize;
  final double fontSize;
  final int favouriteNumber;

  const FavouriteCircularWidget({
    super.key,
    required this.arcSize,
    required this.centerCircleSize,
    required this.fontSize,
    required this.favouriteNumber,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.center,
      children: [
        // Favourite icon display
        Container(
          width: centerCircleSize,
          height: centerCircleSize,
          decoration: BoxDecoration(
            color: Colors.blue,
            shape: BoxShape.circle,
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.favorite,
                color: Colors.yellow,
                size: fontSize,
              ),
              Text(
                'Scene $favouriteNumber',
                style: TextStyle(
                  fontSize: fontSize * 0.4,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
