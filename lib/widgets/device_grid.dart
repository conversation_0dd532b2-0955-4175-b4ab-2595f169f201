import 'package:flutter/material.dart';
import '../models/device_type.dart';
import 'device_button.dart';

class DeviceGrid extends StatelessWidget {
  const DeviceGrid({super.key});

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Determine if we should use a grid or column layout based on width
        final useGrid = constraints.maxWidth > 500;

        if (useGrid) {
          // Grid layout for wider screens
          return GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 3,
            childAspectRatio: 1.2,
            children: [
              Devi<PERSON><PERSON>utton(type: DeviceType.fan, icon: Icons.wind_power_rounded, label: 'Fan'),
              Devi<PERSON><PERSON>utton(type: DeviceType.light, icon: Icons.lightbulb, label: 'Light'),
              DeviceButton(type: DeviceType.power, icon: Icons.power, label: 'Power 1'),
              Devi<PERSON><PERSON>utton(type: DeviceType.power, icon: Icons.power, label: 'Power 2', isSecondPower: true),
              <PERSON><PERSON><PERSON><PERSON>on(type: DeviceType.favourite, icon: Icons.favorite, label: 'Scene - OFF', isFav1: true),
              <PERSON><PERSON><PERSON><PERSON>on(type: DeviceType.favourite, icon: Icons.favorite, label: 'Scene - ON', isFav2: true),
            ],
          );
        } else {
          // Column layout for narrower screens
          return SizedBox(
            height: 240, // Fixed height for the grid
            child: Column(
              children: [
                // First row of devices
                Expanded(
                  child: Row(
                    children: [
                      DeviceButton(type: DeviceType.fan, icon: Icons.wind_power_rounded, label: 'Fan'),
                      DeviceButton(type: DeviceType.light, icon: Icons.lightbulb, label: 'Light'),
                      DeviceButton(type: DeviceType.power, icon: Icons.power, label: 'Power 1'),
                    ],
                  ),
                ),
                // Second row of devices
                Expanded(
                  child: Row(
                    children: [
                      DeviceButton(type: DeviceType.power, icon: Icons.power, label: 'Power 2', isSecondPower: true),
                      DeviceButton(type: DeviceType.favourite, icon: Icons.favorite, label: 'Scene - OFF', isFav1: true),
                      DeviceButton(type: DeviceType.favourite, icon: Icons.favorite, label: 'Scene - ON', isFav2: true),
                    ],
                  ),
                ),
              ],
            ),
          );
        }
      },
    );
  }
}
