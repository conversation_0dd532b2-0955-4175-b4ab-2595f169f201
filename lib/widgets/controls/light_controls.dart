import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../providers/device_state_provider.dart';

class LightControls extends ConsumerStatefulWidget {
  final double maxWidth;

  const LightControls({
    super.key,
    required this.maxWidth,
  });

  @override
  ConsumerState<LightControls> createState() => _LightControlsState();
}

class _LightControlsState extends ConsumerState<LightControls> {
  double? _localBrightness; // Local state for slider
  bool _isDragging = false;

  @override
  Widget build(BuildContext context) {
    final deviceState = ref.watch(deviceStateNotifierProvider);
    final notifier = ref.read(deviceStateNotifierProvider.notifier);

    // Use local state while dragging, otherwise use device state
    final displayBrightness =
        _isDragging && _localBrightness != null
            ? _localBrightness!
            : deviceState.brightness;

    // Adjust sizes based on available width
    final titleSize = widget.maxWidth < 400 ? 16.0 : 18.0;
    final padding = widget.maxWidth < 400 ? 8.0 : 12.0;
    final verticalSpacing = widget.maxWidth < 400 ? 4.0 : 8.0;

    return Container(
      padding: EdgeInsets.all(padding),
      decoration: BoxDecoration(
        color: Colors.grey[850],
        borderRadius: BorderRadius.circular(12),
      ),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Light Control',
                  style: TextStyle(fontSize: titleSize, fontWeight: FontWeight.bold),
                ),
                Switch(
                  value: deviceState.isLightOn,
                  onChanged: (_) => notifier.toggleLight(),
                  activeColor: Colors.blue,
                ),
              ],
            ),
            SizedBox(height: verticalSpacing / 2),
            const Text('Brightness'),
            SliderTheme(
              data: SliderTheme.of(context).copyWith(
                activeTrackColor: deviceState.isLightOn ? Colors.amber : Colors.grey[600],
                inactiveTrackColor:
                    deviceState.isLightOn
                        ? Colors.amber.withValues(alpha: 77)
                    : Colors.grey[800],
                thumbColor: deviceState.isLightOn ? Colors.amber : Colors.grey[400],
                overlayColor:
                    deviceState.isLightOn
                        ? Colors.amber.withValues(alpha: 77)
                    : Colors.grey.withValues(alpha: 77),
              ),
              child: Slider(
                value: displayBrightness,
                min: 0.0,
                max: 1.0,
                divisions: 10,
                label: '${(displayBrightness * 100).round()}%',
                onChanged: (value) {
                  setState(() {
                    _localBrightness = value;
                    _isDragging = true;
                  });
                },
                onChangeEnd: (value) {
                  setState(() {
                    _isDragging = false;
                    _localBrightness = null;
                  });
                  // Only send WebSocket request when slider is released
                  notifier.updateBrightness(value);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
