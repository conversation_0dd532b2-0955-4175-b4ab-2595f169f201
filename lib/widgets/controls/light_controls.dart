import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../providers/device_state_provider.dart';

class LightControls extends ConsumerWidget {
  final double maxWidth;

  const LightControls({
    super.key,
    required this.maxWidth,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final deviceState = ref.watch(deviceStateNotifierProvider);
    final notifier = ref.read(deviceStateNotifierProvider.notifier);
    
    // Adjust sizes based on available width
    final titleSize = maxWidth < 400 ? 16.0 : 18.0;
    final padding = maxWidth < 400 ? 8.0 : 12.0;
    final verticalSpacing = maxWidth < 400 ? 4.0 : 8.0;

    return Container(
      padding: EdgeInsets.all(padding),
      decoration: BoxDecoration(
        color: Colors.grey[850],
        borderRadius: BorderRadius.circular(12),
      ),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Light Control',
                  style: TextStyle(fontSize: titleSize, fontWeight: FontWeight.bold),
                ),
                Switch(
                  value: deviceState.isLightOn,
                  onChanged: (_) => notifier.toggleLight(),
                  activeColor: Colors.blue,
                ),
              ],
            ),
            SizedBox(height: verticalSpacing / 2),
            const Text('Brightness'),
            SliderTheme(
              data: SliderTheme.of(context).copyWith(
                activeTrackColor: deviceState.isLightOn ? Colors.amber : Colors.grey[600],
                inactiveTrackColor: deviceState.isLightOn 
                    ? Colors.amber.withValues(alpha: 77) 
                    : Colors.grey[800],
                thumbColor: deviceState.isLightOn ? Colors.amber : Colors.grey[400],
                overlayColor: deviceState.isLightOn 
                    ? Colors.amber.withValues(alpha: 77) 
                    : Colors.grey.withValues(alpha: 77),
              ),
              child: Slider(
                value: deviceState.brightness,
                min: 0.0,
                max: 1.0,
                divisions: 10,
                label: '${(deviceState.brightness * 100).round()}%',
                onChanged: (value) => notifier.updateBrightness(value),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
