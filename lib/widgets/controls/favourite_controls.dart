import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../providers/device_state_provider.dart';

class FavouriteControls extends ConsumerWidget {
  final double maxWidth;
  final int favouriteNumber;

  const FavouriteControls({
    super.key,
    required this.maxWidth,
    required this.favouriteNumber,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final notifier = ref.read(deviceStateNotifierProvider.notifier);
    
    // Adjust sizes based on available width
    final titleSize = maxWidth < 400 ? 16.0 : 18.0;
    final padding = maxWidth < 400 ? 8.0 : 12.0;
    final verticalSpacing = maxWidth < 400 ? 4.0 : 8.0;
    final buttonPadding = maxWidth < 400
        ? const EdgeInsets.symmetric(horizontal: 16, vertical: 8)
        : const EdgeInsets.symmetric(horizontal: 24, vertical: 12);

    return Container(
      padding: EdgeInsets.all(padding),
      decoration: BoxDecoration(
        color: Colors.grey[850],
        borderRadius: BorderRadius.circular(12),
      ),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Scene $favouriteNumber',
              style: TextStyle(fontSize: titleSize, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: verticalSpacing),
            Center(
              child: ElevatedButton(
                onPressed: () => notifier.executeFavouriteAction(favouriteNumber),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  padding: buttonPadding,
                ),
                child: const Text('Execute Action'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
