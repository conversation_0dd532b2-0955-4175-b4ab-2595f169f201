import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../providers/device_state_provider.dart';

class FanControls extends ConsumerStatefulWidget {
  final double maxWidth;

  const FanControls({
    super.key,
    required this.maxWidth,
  });

  @override
  ConsumerState<FanControls> createState() => _FanControlsState();
}

class _FanControlsState extends ConsumerState<FanControls> {
  int? _localFanLevel; // Local state for slider
  bool _isDragging = false;

  @override
  Widget build(BuildContext context) {
    final deviceState = ref.watch(deviceStateNotifierProvider);
    final notifier = ref.read(deviceStateNotifierProvider.notifier);

    // Use local state while dragging, otherwise use device state
    final displayLevel =
        _isDragging && _localFanLevel != null
            ? _localFanLevel!
            : deviceState.fanLevel;

    // Adjust sizes based on available width
    final titleSize = widget.maxWidth < 400 ? 16.0 : 18.0;
    final padding = widget.maxWidth < 400 ? 8.0 : 12.0;
    final verticalSpacing = widget.maxWidth < 400 ? 4.0 : 8.0;

    return Container(
      padding: EdgeInsets.all(padding),
      decoration: BoxDecoration(
        color: Colors.grey[850],
        borderRadius: BorderRadius.circular(12),
      ),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Fan Control',
                  style: TextStyle(fontSize: titleSize, fontWeight: FontWeight.bold),
                ),
                Switch(
                  value: deviceState.isFanOn,
                  onChanged: (_) => notifier.toggleFan(),
                  activeColor: Colors.blue,
                ),
              ],
            ),
            SizedBox(height: verticalSpacing / 2),
            const Text('Fan Level'),
            Slider(
              value: displayLevel > 4 ? 4.0 : displayLevel.toDouble(),
              min: 0,
              max: 4,
              divisions: 4,
              label: 'Level ${displayLevel > 4 ? 4 : displayLevel}',
              onChanged: (value) {
                setState(() {
                  _localFanLevel = value.toInt();
                  _isDragging = true;
                });
              },
              onChangeEnd: (value) {
                setState(() {
                  _isDragging = false;
                  _localFanLevel = null;
                });
                // Only send WebSocket request when slider is released
                notifier.updateFanSpeed(value.toInt());
              },
            ),
          ],
        ),
      ),
    );
  }
}
