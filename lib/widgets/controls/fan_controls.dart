import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../providers/device_state_provider.dart';

class FanControls extends ConsumerWidget {
  final double maxWidth;

  const FanControls({
    super.key,
    required this.maxWidth,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final deviceState = ref.watch(deviceStateNotifierProvider);
    final notifier = ref.read(deviceStateNotifierProvider.notifier);
    
    // Adjust sizes based on available width
    final titleSize = maxWidth < 400 ? 16.0 : 18.0;
    final padding = maxWidth < 400 ? 8.0 : 12.0;
    final verticalSpacing = maxWidth < 400 ? 4.0 : 8.0;

    return Container(
      padding: EdgeInsets.all(padding),
      decoration: BoxDecoration(
        color: Colors.grey[850],
        borderRadius: BorderRadius.circular(12),
      ),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Fan Control',
                  style: TextStyle(fontSize: titleSize, fontWeight: FontWeight.bold),
                ),
                Switch(
                  value: deviceState.isFanOn,
                  onChanged: (_) => notifier.toggleFan(),
                  activeColor: Colors.blue,
                ),
              ],
            ),
            SizedBox(height: verticalSpacing / 2),
            const Text('Fan Level'),
            Slider(
              value: deviceState.fanLevel > 4 ? 4.0 : deviceState.fanLevel.toDouble(),
              min: 0,
              max: 4,
              divisions: 4,
              label: 'Level ${deviceState.fanLevel > 4 ? 4 : deviceState.fanLevel}',
              onChanged: (value) => notifier.updateFanSpeed(value.toInt()),
            ),
          ],
        ),
      ),
    );
  }
}
