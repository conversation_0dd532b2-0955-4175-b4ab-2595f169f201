import 'package:flutter/material.dart';
import 'dart:math' as math;

class BrightnessArcPainter extends CustomPainter {
  final double progress; // 0.0 to 1.0

  BrightnessArcPainter(this.progress);

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2 - 10;

    // Background arc
    final bgPaint = Paint()
      ..color = Colors.grey[700]!
      ..style = PaintingStyle.stroke
      ..strokeWidth = 10
      ..strokeCap = StrokeCap.round;

    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      math.pi * 0.8, // Start angle
      math.pi * 1.4, // Sweep angle
      false,
      bgPaint,
    );

    // Progress arc
    final progressAngle = math.pi * 1.4 * progress;
    final progressPaint = Paint()
      ..color = Colors.amber
      ..style = PaintingStyle.stroke
      ..strokeWidth = 10
      ..strokeCap = StrokeCap.round;

    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      math.pi * 0.8, // Start angle
      progressAngle, // Sweep angle based on progress
      false,
      progressPaint,
    );

    // Draw the indicator dot
    final dotAngle = math.pi * 0.8 + progressAngle;
    final dotX = center.dx + radius * math.cos(dotAngle);
    final dotY = center.dy + radius * math.sin(dotAngle);

    final dotPaint = Paint()..color = Colors.amber;
    canvas.drawCircle(Offset(dotX, dotY), 5, dotPaint);
  }

  @override
  bool shouldRepaint(covariant BrightnessArcPainter oldDelegate) {
    return oldDelegate.progress != progress;
  }
}
