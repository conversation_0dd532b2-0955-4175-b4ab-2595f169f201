import 'package:flutter/material.dart';
import 'dart:math' as math;

class PowerArcPainter extends CustomPainter {
  final double progress; // 0.0 or 1.0

  PowerArcPainter(this.progress);

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2 - 10;

    // Background arc
    final bgPaint = Paint()
      ..color = Colors.grey[700]!
      ..style = PaintingStyle.stroke
      ..strokeWidth = 10
      ..strokeCap = StrokeCap.round;

    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      math.pi * 0.8, // Start angle
      math.pi * 1.4, // Sweep angle
      false,
      bgPaint,
    );

    if (progress > 0) {
      // Progress arc (full when on, none when off)
      final progressPaint = Paint()
        ..color = progress > 0 ? Colors.green : Colors.red
        ..style = PaintingStyle.stroke
        ..strokeWidth = 10
        ..strokeCap = StrokeCap.round;

      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius),
        math.pi * 0.8, // Start angle
        math.pi * 1.4, // Full sweep when on
        false,
        progressPaint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant PowerArcPainter oldDelegate) {
    return oldDelegate.progress != progress;
  }
}
