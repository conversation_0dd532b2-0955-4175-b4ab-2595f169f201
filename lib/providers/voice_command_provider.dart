import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/voice_command_service.dart';

final voiceCommandServiceProvider = Provider<VoiceCommandService>((ref) {
  final service = VoiceCommandService();
  
  ref.onDispose(() {
    service.dispose();
  });
  
  return service;
});

// Provider for the current listening state
final isListeningProvider = StateProvider<bool>((ref) => false);

// Provider for the last recognized words
final lastRecognizedWordsProvider = StateProvider<String>((ref) => '');
