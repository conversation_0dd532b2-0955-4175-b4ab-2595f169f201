import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/matter_device.dart';
import '../services/matter_binding_service.dart';

// Matter binding service provider
final matterBindingServiceProvider = Provider<MatterBindingService>((ref) {
  final service = MatterBindingService();
  service.initializeDevices();
  
  ref.onDispose(() {
    service.dispose();
  });
  
  return service;
});

// Matter devices stream provider
final matterDevicesProvider = StreamProvider<List<MatterDevice>>((ref) {
  final service = ref.watch(matterBindingServiceProvider);
  return service.devicesStream;
});

// Matter bindings stream provider
final matterBindingsProvider = StreamProvider<List<MatterBinding>>((ref) {
  final service = ref.watch(matterBindingServiceProvider);
  return service.bindingsStream;
});

// Controller devices provider
final controllerDevicesProvider = Provider<List<MatterDevice>>((ref) {
  final service = ref.watch(matterBindingServiceProvider);
  return service.getControllerDevices();
});

// Controlled devices provider
final controlledDevicesProvider = Provider<List<MatterDevice>>((ref) {
  final service = ref.watch(matterBindingServiceProvider);
  return service.getControlledDevices();
});

// Matter binding state notifier
class MatterBindingState {
  final bool isCommissioning;
  final bool isCreatingBinding;
  final String? currentOperation;
  final String? errorMessage;

  const MatterBindingState({
    this.isCommissioning = false,
    this.isCreatingBinding = false,
    this.currentOperation,
    this.errorMessage,
  });

  MatterBindingState copyWith({
    bool? isCommissioning,
    bool? isCreatingBinding,
    String? currentOperation,
    String? errorMessage,
  }) {
    return MatterBindingState(
      isCommissioning: isCommissioning ?? this.isCommissioning,
      isCreatingBinding: isCreatingBinding ?? this.isCreatingBinding,
      currentOperation: currentOperation ?? this.currentOperation,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }
}

class MatterBindingNotifier extends StateNotifier<MatterBindingState> {
  final MatterBindingService _service;

  MatterBindingNotifier(this._service) : super(const MatterBindingState());

  Future<bool> commissionDevice(String entityId, String shareCode, int nodeId) async {
    state = state.copyWith(
      isCommissioning: true,
      currentOperation: 'Commissioning device to CHIPTool...',
      errorMessage: null,
    );

    try {
      final success = await _service.commissionDeviceToChipTool(entityId, shareCode, nodeId);
      
      if (success) {
        state = state.copyWith(
          isCommissioning: false,
          currentOperation: null,
        );
      } else {
        state = state.copyWith(
          isCommissioning: false,
          currentOperation: null,
          errorMessage: 'Failed to commission device to CHIPTool',
        );
      }
      
      return success;
    } catch (e) {
      state = state.copyWith(
        isCommissioning: false,
        currentOperation: null,
        errorMessage: 'Error commissioning device: $e',
      );
      return false;
    }
  }

  Future<bool> createBinding(
    MatterDevice controller,
    MatterDevice controlled,
    int controllerEndpoint,
    int controlledEndpoint,
    int clusterId,
  ) async {
    state = state.copyWith(
      isCreatingBinding: true,
      currentOperation: 'Creating Matter binding...',
      errorMessage: null,
    );

    try {
      final success = await _service.createBinding(
        controller,
        controlled,
        controllerEndpoint,
        controlledEndpoint,
        clusterId,
      );
      
      if (success) {
        state = state.copyWith(
          isCreatingBinding: false,
          currentOperation: null,
        );
      } else {
        state = state.copyWith(
          isCreatingBinding: false,
          currentOperation: null,
          errorMessage: 'Failed to create Matter binding',
        );
      }
      
      return success;
    } catch (e) {
      state = state.copyWith(
        isCreatingBinding: false,
        currentOperation: null,
        errorMessage: 'Error creating binding: $e',
      );
      return false;
    }
  }

  Future<bool> removeBinding(String bindingId) async {
    state = state.copyWith(
      isCreatingBinding: true,
      currentOperation: 'Removing Matter binding...',
      errorMessage: null,
    );

    try {
      final success = await _service.removeBinding(bindingId);
      
      state = state.copyWith(
        isCreatingBinding: false,
        currentOperation: null,
        errorMessage: success ? null : 'Failed to remove binding',
      );
      
      return success;
    } catch (e) {
      state = state.copyWith(
        isCreatingBinding: false,
        currentOperation: null,
        errorMessage: 'Error removing binding: $e',
      );
      return false;
    }
  }

  void clearError() {
    state = state.copyWith(errorMessage: null);
  }
}

final matterBindingNotifierProvider = StateNotifierProvider<MatterBindingNotifier, MatterBindingState>((ref) {
  final service = ref.watch(matterBindingServiceProvider);
  return MatterBindingNotifier(service);
});
