import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/home_assistant_service.dart';

// Removed auto-generated part file
// part 'home_assistant_provider.g.dart';

// Converted from @Riverpod annotation to manual provider
final homeAssistantServiceProvider = Provider<HomeAssistantService>((ref) {
  final service = HomeAssistantService();

  // Connect to Home Assistant when the provider is created
  service.connect();

  // Dispose of the service when the provider is disposed
  ref.onDispose(() {
    service.dispose();
  });

  return service;
});
