import 'dart:async';
import 'dart:developer';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/device_state.dart';
import '../models/entity_ids.dart';
// Removed unused import: '../models/device_type.dart';
// Commented out Firebase analytics service
// import '../services/analytics_service.dart';
import 'home_assistant_provider.dart';

// Removed auto-generated part file
// part 'device_state_provider.g.dart';

// Converted from @Riverpod annotation to manual provider
class DeviceStateNotifier extends StateNotifier<DeviceState> {
  final Ref ref;

  // Analytics service instance - Commented out Firebase analytics
  // final AnalyticsService _analytics = AnalyticsService();

  Timer? _stateRefreshTimer;

  DeviceStateNotifier(this.ref) : super(const DeviceState()) {
    _initialize();
  }

  void _initialize() {
    // Initialize analytics service - Commented out Firebase analytics
    // _analytics.initialize();

    // Track app session start - Commented out Firebase analytics
    // _analytics.trackAppSession(isStart: true);

    // Listen to state changes from Home Assistant
    final service = ref.read(homeAssistantServiceProvider);
    service.stateChanges.listen(_handleStateChange);

    // Update connection state
    ref.listen(homeAssistantServiceProvider, (previous, next) {
      state = state.copyWith(isConnected: next.isConnected);

      // Start/stop periodic state refresh based on connection status
      if (next.isConnected && _stateRefreshTimer == null) {
        _startPeriodicStateRefresh();
      } else if (!next.isConnected && _stateRefreshTimer != null) {
        _stopPeriodicStateRefresh();
      }

      // Track connectivity status - Commented out Firebase analytics
      // if (previous?.isConnected != next.isConnected) {
      //   _analytics.trackError(
      //     errorType: next.isConnected ? 'connection_restored' : 'connection_lost',
      //     errorMessage: next.isConnected
      //         ? 'Connection to Home Assistant restored'
      //         : 'Connection to Home Assistant lost',
      //   );
      // }
    });
  }

  void _startPeriodicStateRefresh() {
    log('Starting periodic state refresh every 30 seconds');
    _stateRefreshTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      final service = ref.read(homeAssistantServiceProvider);
      if (service.isConnected) {
        log('Requesting current states for all entities');
        service.requestCurrentStates();

        // Also check connection health
        service.checkConnectionHealth();
      } else {
        log('Service not connected, attempting to reconnect...');
        service.connect();
      }
    });
  }

  void _stopPeriodicStateRefresh() {
    log('Stopping periodic state refresh');
    _stateRefreshTimer?.cancel();
    _stateRefreshTimer = null;
  }

  void _handleStateChange(Map<String, dynamic> data) {
    // Check if new_state is not null
    if (!data.containsKey('new_state') || data['new_state'] == null) {
      log('Error: Missing new_state in _handleStateChange');
      // Track error - Commented out Firebase analytics
      // _analytics.trackError(
      //   errorType: 'state_change_error',
      //   errorMessage: 'Missing new_state in _handleStateChange',
      //   context: {'data': data},
      // );
      return;
    }

    final entityId = data['entity_id'];
    final newState = data['new_state']['state'];
    final attributes = data['new_state']['attributes'];

    // Enhanced availability detection
    bool isOnline = true;
    if (attributes != null) {
      final availability = attributes['availability'];
      if (availability != null) {
        isOnline = availability == 'online';
      }
    }

    // Also check if the state indicates the device is unavailable
    if (newState == 'unavailable' || newState == 'unknown') {
      isOnline = false;
    }

    log(
      'Received state change for entity: $entityId, new state: $newState, online: $isOnline',
    );
    if (attributes != null) {
      log('Attributes: $attributes');
    }

    // Update fan state
    if (entityId == EntityIds.defaultIds.fan) {
      _updateFanState(newState, data['new_state']['attributes']);
      state = state.copyWith(isFanOnline: isOnline);
      log('Fan online status: $isOnline');
    }
    // Update light state
    else if (entityId == EntityIds.defaultIds.light) {
      log('Updating light state: $newState with attributes: ${data['new_state']['attributes']}');
      _updateLightState(newState, data['new_state']['attributes']);
      state = state.copyWith(isLightOnline: isOnline);
      log('Light online status: $isOnline');
    }
    // Update power 1 state
    else if (entityId == EntityIds.defaultIds.power1) {
      state = state.copyWith(isPower1On: newState == 'on', isPower1Online: isOnline);
      log('Power 1 online status: $isOnline');
    }
    // Update power 2 state
    else if (entityId == EntityIds.defaultIds.power2) {
      state = state.copyWith(isPower2On: newState == 'on', isPower2Online: isOnline);
      log('Power 2 online status: $isOnline');
    }
    // Update favorite 1 state
    else if (entityId == EntityIds.defaultIds.favourite1) {
      state = state.copyWith(isFavourite1On: newState == 'on', isFavourite1Online: isOnline);
      log('Favorite 1 light state updated: ${state.isFavourite1On ? 'ON' : 'OFF'}, online: $isOnline');
    }
    // Update favorite 2 state
    else if (entityId == EntityIds.defaultIds.favourite2) {
      state = state.copyWith(isFavourite2On: newState == 'on', isFavourite2Online: isOnline);
      log('Favorite 2 light state updated: ${state.isFavourite2On ? 'ON' : 'OFF'}, online: $isOnline');
    }

    // Track device connectivity changes - Commented out Firebase analytics
    // Removed unused variables: deviceType and previousOnlineState
  }

  void _updateFanState(String newState, Map<String, dynamic>? attributes) {
    final isFanOn = newState == 'on';
    final isFanOnline = state.isFanOnline; // Preserve online status

    log('Updating fan state: newState=$newState, isFanOn=$isFanOn');

    if (attributes != null && attributes['percentage'] != null) {
      final percentage = attributes['percentage'];
      log('Received fan percentage from device: $percentage');

      // Get raw percentage value for debugging
      int rawPercentage = percentage.toInt();

      // Map percentage ranges to specific levels (0-4) with more precise mapping
      int newFanLevel;
      if (rawPercentage <= 0) {
        newFanLevel = 0; // Off
      } else if (rawPercentage <= 25) {
        newFanLevel = 1; // Level 1 (1-25%)
      } else if (rawPercentage <= 50) {
        newFanLevel = 2; // Level 2 (26-50%)
      } else if (rawPercentage <= 75) {
        newFanLevel = 3; // Level 3 (51-75%)
      } else {
        newFanLevel = 4; // Level 4 (76-100%)
      }

      // Validate the fan level
      newFanLevel = _validateFanLevel(newFanLevel);

      log(
        'Mapped percentage $rawPercentage% to fan level: $newFanLevel (previous: ${state.fanLevel})',
      );

      // Update state with new values
      state = state.copyWith(
        isFanOn: isFanOn,
        fanLevel: newFanLevel,
        isFanOnline: isFanOnline,
      );
    } else {
      // If no percentage attribute, handle based on state only
      if (!isFanOn) {
        // Fan is off, set level to 0
        log('Fan is off, setting level to 0');
        state = state.copyWith(
          isFanOn: false,
          fanLevel: 0,
          isFanOnline: isFanOnline,
        );
      } else {
        // Fan is on but no percentage, keep current level or set to 1 if 0
        int currentLevel = state.fanLevel;
        if (currentLevel == 0) {
          currentLevel = 1; // Default to level 1 if fan is on but level was 0
          log('Fan is on but level was 0, setting to level 1');
        }
        state = state.copyWith(
          isFanOn: true,
          fanLevel: currentLevel,
          isFanOnline: isFanOnline,
        );
      }
    }

    log(
      'Fan state updated: isFanOn=${state.isFanOn}, fanLevel=${state.fanLevel}, isFanOnline=${state.isFanOnline}',
    );
  }

  void _updateLightState(String newState, Map<String, dynamic>? attributes) {
    final isLightOn = newState == 'on';
    final isLightOnline = state.isLightOnline; // Preserve online status
    log('Light is ${isLightOn ? "ON" : "OFF"}');

    if (isLightOn && attributes != null && attributes['brightness'] != null) {
      final rawBrightness = attributes['brightness'];
      final brightness = rawBrightness / 255.0; // Convert 0-255 to 0.0-1.0
      log('Light brightness: $rawBrightness (raw) -> $brightness (normalized)');
      state = state.copyWith(isLightOn: isLightOn, brightness: brightness, isLightOnline: isLightOnline);
    } else {
      log('No brightness attribute found or light is off');
      state = state.copyWith(isLightOn: isLightOn, isLightOnline: isLightOnline);
    }

    log('Updated light state: isOn=${state.isLightOn}, brightness=${state.brightness}, online=${state.isLightOnline}');
  }

  // Helper method to ensure fan level is within valid range
  int _validateFanLevel(int level) {
    if (level < 0) {
      log('WARNING: Fan level $level is below minimum of 0, setting to 0');
      return 0;
    } else if (level > 4) {
      log('WARNING: Fan level $level exceeds maximum of 4, capping at 4');
      return 4;
    }
    return level;
  }

  // Fan control methods
  void toggleFan() {
    log(
      'toggleFan called: current state - isFanOn=${state.isFanOn}, fanLevel=${state.fanLevel}',
    );

    // If turning on and level is 0, set to minimum level 1
    int fanLevel = state.fanLevel;
    if (fanLevel < 1 && !state.isFanOn) {
      log('Setting fan to minimum level 1 when turning on');
      fanLevel = 1;
    }

    final newIsFanOn = !state.isFanOn;
    log('Toggling fan: newIsFanOn=$newIsFanOn, fanLevel=$fanLevel');

    // Update local state immediately for UI responsiveness
    state = state.copyWith(isFanOn: newIsFanOn, fanLevel: fanLevel);

    final service = ref.read(homeAssistantServiceProvider);
    service.toggleFan(EntityIds.defaultIds.fan, newIsFanOn, fanLevel);

    // Track the command - Commented out Firebase analytics
    // _analytics.trackDeviceCommand(
    //   deviceType: 'fan',
    //   entityId: EntityIds.defaultIds.fan,
    //   command: newIsFanOn ? 'turn_on' : 'turn_off',
    //   isSuccess: true,
    //   parameters: {'level': fanLevel},
    // );
  }

  void updateFanSpeed(int level) {
    log(
      'updateFanSpeed called: requested level=$level, current state - isFanOn=${state.isFanOn}, fanLevel=${state.fanLevel}',
    );

    // Ensure level is within valid range
    level = _validateFanLevel(level);

    log('Setting fan speed to validated level: $level');

    // Set fan on only if level > 0
    final isFanOn = level > 0;

    // Update local state immediately for UI responsiveness
    state = state.copyWith(fanLevel: level, isFanOn: isFanOn);

    log('Updated local state: isFanOn=$isFanOn, fanLevel=$level');

    final service = ref.read(homeAssistantServiceProvider);
    service.setFanSpeed(EntityIds.defaultIds.fan, level);

    // Track the command - Commented out Firebase analytics
    // _analytics.trackDeviceCommand(
    //   deviceType: 'fan',
    //   entityId: EntityIds.defaultIds.fan,
    //   command: 'set_speed',
    //   isSuccess: true,
    //   parameters: {'level': level, 'turned_on': isFanOn},
    // );
  }

  // Light control methods
  void toggleLight() {
    final newIsLightOn = !state.isLightOn;
    state = state.copyWith(isLightOn: newIsLightOn);

    final service = ref.read(homeAssistantServiceProvider);
    service.toggleLight(EntityIds.defaultIds.light, newIsLightOn);

    // Track the command - Commented out Firebase analytics
    // _analytics.trackDeviceCommand(
    //   deviceType: 'light',
    //   entityId: EntityIds.defaultIds.light,
    //   command: newIsLightOn ? 'turn_on' : 'turn_off',
    //   isSuccess: true,
    //   parameters: {'brightness': state.brightness},
    // );
  }

  void updateBrightness(double value) {
    log('Updating brightness to $value, turning light on');
    state = state.copyWith(brightness: value, isLightOn: true);

    final service = ref.read(homeAssistantServiceProvider);
    service.setBrightness(EntityIds.defaultIds.light, value);

    // Track the command - Commented out Firebase analytics
    // _analytics.trackDeviceCommand(
    //   deviceType: 'light',
    //   entityId: EntityIds.defaultIds.light,
    //   command: 'set_brightness',
    //   isSuccess: true,
    //   parameters: {'brightness': value, 'turned_on': true},
    // );
  }

  // Power control methods
  void togglePower1() {
    final newIsPower1On = !state.isPower1On;
    state = state.copyWith(isPower1On: newIsPower1On);

    final service = ref.read(homeAssistantServiceProvider);
    service.togglePower(EntityIds.defaultIds.power1, newIsPower1On);

    // Track the command - Commented out Firebase analytics
    // _analytics.trackDeviceCommand(
    //   deviceType: 'power',
    //   entityId: EntityIds.defaultIds.power1,
    //   command: newIsPower1On ? 'turn_on' : 'turn_off',
    //   isSuccess: true,
    //   parameters: {'outlet': 1},
    // );
  }

  void togglePower2() {
    final newIsPower2On = !state.isPower2On;
    state = state.copyWith(isPower2On: newIsPower2On);

    final service = ref.read(homeAssistantServiceProvider);
    service.togglePower(EntityIds.defaultIds.power2, newIsPower2On);

    // Track the command - Commented out Firebase analytics
    // _analytics.trackDeviceCommand(
    //   deviceType: 'power',
    //   entityId: EntityIds.defaultIds.power2,
    //   command: newIsPower2On ? 'turn_on' : 'turn_off',
    //   isSuccess: true,
    //   parameters: {'outlet': 2},
    // );
  }

  // Favorite control methods
  void executeFavouriteAction(int favouriteNumber) {
    String entityId;
    bool isCurrentlyOn;
    // Removed unused variable: String sceneName;

    if (favouriteNumber == 1) {
      // First favorite button is for OFF action
      entityId = EntityIds.defaultIds.favourite1;
      isCurrentlyOn = state.isFavourite1On;
    } else {
      // Second favorite button is for ON action
      entityId = EntityIds.defaultIds.favourite2;
      isCurrentlyOn = state.isFavourite2On;
    }

    // Toggle the state (turn off if on, turn on if off)
    final newIsOn = !isCurrentlyOn;

    log('Executing Favourite $favouriteNumber action: ${newIsOn ? "turn_on" : "turn_off"} for $entityId');
    log('Current state: ${isCurrentlyOn ? "ON" : "OFF"}');

    final service = ref.read(homeAssistantServiceProvider);
    service.toggleLight(entityId, newIsOn);

    // Update state (will be overridden by the state change event from Home Assistant)
    if (favouriteNumber == 1) {
      state = state.copyWith(isFavourite1On: newIsOn);
    } else {
      state = state.copyWith(isFavourite2On: newIsOn);
    }

    // Track the command - Commented out Firebase analytics
    // _analytics.trackDeviceCommand(
    //   deviceType: 'scene',
    //   entityId: entityId,
    //   command: newIsOn ? 'activate_scene' : 'deactivate_scene',
    //   isSuccess: true,
    //   parameters: {
    //     'scene_number': favouriteNumber,
    //     'scene_name': sceneName,
    //     'previous_state': isCurrentlyOn ? 'on' : 'off',
    //   },
    // );
  }

  // This method is no longer used - we're using selectedDeviceProvider instead
  // void setSelectedDevice(String device) {
  //   state = state.copyWith(selectedDevice: device);
  // }

  @override
  void dispose() {
    _stopPeriodicStateRefresh();
    super.dispose();
  }
}

// Create the provider instance
final deviceStateNotifierProvider =
    StateNotifierProvider<DeviceStateNotifier, DeviceState>((ref) {
      return DeviceStateNotifier(ref);
    });
