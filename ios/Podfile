# Uncomment this line to define a global platform for your project
platform :ios, '12.0'

# CocoaPods analytics sends network stats synchronously affecting flutter build latency.
ENV['COCOAPODS_DISABLE_STATS'] = 'true'

project 'Runner', {
  'Debug' => :debug,
  'Profile' => :release,
  'Release' => :release,
}

def flutter_root
  generated_xcode_build_settings_path = File.expand_path(File.join('..', 'Flutter', 'Generated.xcconfig'), __FILE__)
  unless File.exist?(generated_xcode_build_settings_path)
    raise "#{generated_xcode_build_settings_path} must exist. If you're running pod install manually, make sure flutter pub get is executed first"
  end

  File.foreach(generated_xcode_build_settings_path) do |line|
    matches = line.match(/FLUTTER_ROOT\=(.*)/)
    return matches[1].strip if matches
  end
  raise "FLUTTER_ROOT not found in #{generated_xcode_build_settings_path}. Try deleting Generated.xcconfig, then run flutter pub get"
end

require File.expand_path(File.join('packages', 'flutter_tools', 'bin', 'podhelper'), flutter_root)

flutter_ios_podfile_setup

target 'Runner' do
  use_frameworks!

  flutter_install_all_ios_pods File.dirname(File.realpath(__FILE__))
  target 'RunnerTests' do
    inherit! :search_paths
  end
end

post_install do |installer|
  installer.pods_project.targets.each do |target|
    flutter_additional_ios_build_settings(target)

    # Fix for iOS 16+ and Xcode 15+
    target.build_configurations.each do |config|
      # Set minimum deployment target
      config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '12.0'

      # Fix for unsupported compiler flags and options
      config.build_settings.delete('GCC_WARN_INHIBIT_ALL_WARNINGS')

      # Remove ALL problematic debug flags including -G
      ['OTHER_CFLAGS', 'OTHER_CPLUSPLUSFLAGS', 'OTHER_SWIFT_FLAGS'].each do |key|
        if config.build_settings[key]
          config.build_settings[key] = config.build_settings[key].reject { |flag|
            flag.include?('-G') || flag == '-G' || flag.start_with?('-G')
          }
        end
      end

      # Remove debug info flags that might cause issues
      config.build_settings['GCC_GENERATE_DEBUGGING_SYMBOLS'] = 'NO'
      config.build_settings['DEBUG_INFORMATION_FORMAT'] = 'dwarf'
      config.build_settings['ONLY_ACTIVE_ARCH'] = 'NO'

      # Fix for arm64 simulator on Apple Silicon
      config.build_settings["EXCLUDED_ARCHS[sdk=iphonesimulator*]"] = "arm64"

      # Disable bitcode (deprecated in Xcode 14)
      config.build_settings['ENABLE_BITCODE'] = 'NO'

      # Set Swift version
      config.build_settings['SWIFT_VERSION'] = '5.0'

      # Fix for iOS 18 compatibility
      config.build_settings['CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER'] = 'NO'

      # Fix for permission handler
      config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] ||= []
      config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] << 'PERMISSION_EVENTS=1'

      # Fix for newer Xcode versions
      config.build_settings['DEAD_CODE_STRIPPING'] = 'YES'
      config.build_settings['LIBRARY_SEARCH_PATHS'] = ['$(inherited)', '$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)']

      # Additional fixes for compilation issues
      config.build_settings['CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES'] = 'YES'
      config.build_settings['DEFINES_MODULE'] = 'YES'
    end
  end
end
